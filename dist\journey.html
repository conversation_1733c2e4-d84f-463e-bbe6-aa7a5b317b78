<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monoloci: Live Journey</title>

    <!-- Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval'
            https://www.gstatic.com
            https://maps.googleapis.com
            https://cdn.tailwindcss.com
            https://www.googletagmanager.com;
        worker-src 'self' blob:;
        style-src 'self' 'unsafe-inline'
            https://fonts.googleapis.com
            https://cdn.tailwindcss.com;
        font-src 'self'
            https://fonts.gstatic.com;
        img-src 'self' data: blob:
            https://firebasestorage.googleapis.com
            https://aura-app-backend.firebasestorage.app
            https://monoloci.xyz
            https://maps.googleapis.com
            https://maps.gstatic.com;
        connect-src 'self'
            https://firestore.googleapis.com
            https://firebase.googleapis.com
            https://firebasestorage.googleapis.com
            https://firebaseinstallations.googleapis.com
            https://identitytoolkit.googleapis.com
            https://securetoken.googleapis.com
            https://maps.googleapis.com
            https://www.google-analytics.com
            wss://firestore.googleapis.com;
        frame-src 'none';
        object-src 'none';
        base-uri 'self';
        form-action 'self';
        upgrade-insecure-requests;
    ">
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Secure Configuration - Load before app scripts -->
    <script src="./config.js"></script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./solo.css">
    <!-- Google Maps API will be loaded dynamically by journey.js -->
    <style>
        #map {
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div id="status-overlay" class="fixed top-4 left-1/2 -translate-x-1/2 bg-white p-4 rounded-lg shadow-lg text-center">
        <p id="status-text" class="text-slate-800 font-semibold">Connecting to journey...</p>
    </div>
    <script type="module" src="./journey.js"></script>
</body>
</html>