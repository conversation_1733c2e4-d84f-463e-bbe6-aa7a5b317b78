<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Codex Data Seeder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        @import url('https://rsms.me/inter/inter.css');
    </style>
</head>
<body class="bg-gray-900 text-gray-100 flex items-center justify-center min-h-screen">

    <div id="main-container" class="text-center bg-gray-800 p-8 rounded-lg shadow-2xl border border-gray-700 max-w-lg">
        <h1 class="text-3xl font-bold text-cyan-300 mb-4">Codex Database Seeder</h1>
        <p id="status" class="text-gray-400 mb-6">This tool will populate your Firestore database with the initial set of 5 sacred nodes for the Codex feature. Make sure your Firebase configuration is correct.</p>
        
        <button id="seed-button" class="bg-cyan-500 text-gray-900 font-bold py-3 px-6 rounded-lg hover:bg-cyan-400 transition-colors duration-300 transform hover:scale-105 w-full">
            Seed Database
        </button>

        <div id="log-container" class="mt-6 text-left bg-gray-900/50 p-4 rounded-lg max-h-60 overflow-y-auto hidden">
            <pre id="log" class="text-xs text-gray-300 whitespace-pre-wrap"></pre>
        </div>
    </div>

    <script type="module">
        // --- Firebase SDKs ---
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getFirestore, doc, setDoc, writeBatch } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // --- Configuration (from your app.js) ---
        const firebaseConfig = {
            apiKey: "AIzaSyBA0X9xdiOL3nNdqzKibPIZVSciG88iR6Q",
            authDomain: "aura-app-backend.firebaseapp.com",
            projectId: "aura-app-backend",
            storageBucket: "aura-app-backend.firebasestorage.app",
            messagingSenderId: "905604520531",
            appId: "1:905604520531:web:bc77bae93a6a9ff7c104a6"
        };

        // --- Data to Seed ---
        const sacredNodes = [
            { id: 'node_water_serpent', name: 'The Water Serpent', tone: 'C3', glyph: 'M3 12h18M3 6h18M3 18h18' },
            { id: 'node_earth_turtle', name: 'The Earth Turtle', tone: 'G3', glyph: 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z' },
            { id: 'node_fire_dragon', name: 'The Fire Dragon', tone: 'E4', glyph: 'M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5' },
            { id: 'node_air_eagle', name: 'The Air Eagle', tone: 'A4', glyph: 'M17.5 19H9a7 7 0 1 1 0-14h8.5a5.5 5.5 0 1 1 0 11z' },
            { id: 'node_aether_lotus', name: 'The Aether Lotus', tone: 'C5', glyph: 'M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8a8 8 0 0 1-8 8z' }
        ];

        // --- Seeding Logic ---
        const seedButton = document.getElementById('seed-button');
        const statusEl = document.getElementById('status');
        const logContainer = document.getElementById('log-container');
        const logEl = document.getElementById('log');

        function log(message, isError = false) {
            console.log(message);
            logContainer.classList.remove('hidden');
            const color = isError ? 'text-red-400' : 'text-green-400';
            logEl.innerHTML += `<span class="${color}">></span> ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        seedButton.addEventListener('click', async () => {
            seedButton.disabled = true;
            seedButton.textContent = 'Seeding...';
            statusEl.textContent = 'Connecting to Firebase...';

            try {
                // Initialize Firebase
                const app = initializeApp(firebaseConfig);
                const db = getFirestore(app);
                log('Firebase Initialized Successfully.');

                // Create a batch write
                const batch = writeBatch(db);
                const codexId = 'codex'; // As used in your app

                log(`Preparing to write ${sacredNodes.length} nodes to collection: codexes/${codexId}/nodes`);

                sacredNodes.forEach(node => {
                    const nodeRef = doc(db, `codexes/${codexId}/nodes`, node.id);
                    batch.set(nodeRef, node);
                    log(`  - Queued write for ${node.name} (${node.id})`);
                });

                // Commit the batch
                await batch.commit();
                
                log('Batch commit successful!');
                statusEl.innerHTML = '<strong class="text-green-400">Database seeded successfully!</strong> You can now close this page and reload the app.';
                seedButton.className = "bg-green-500 text-white font-bold py-3 px-6 rounded-lg w-full";
                seedButton.textContent = 'Success!';

            } catch (error) {
                console.error("Seeding failed:", error);
                log(`ERROR: ${error.message}`, true);
                statusEl.innerHTML = `<strong class="text-red-400">Seeding failed.</strong> Check the console for details. Common issues include incorrect Firebase project ID or Firestore not being enabled.`;
                seedButton.disabled = false;
                seedButton.textContent = 'Try Again';
            }
        });
    </script>
</body>
</html>