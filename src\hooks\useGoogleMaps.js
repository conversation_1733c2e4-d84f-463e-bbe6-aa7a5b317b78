import { useState, useEffect } from 'react';

const SCRIPT_ID = 'google-maps-script';
const API_KEY = 'AIzaSyDwwiFkI0LfJaYPuixc8fKo53pJITUQpMs'; // This should ideally be in an env file
const LIBRARIES = ['marker', 'geometry'];

let isLoaded = false;
let error = null;
let loadingPromise = null;

const useGoogleMaps = () => {
    const [state, setState] = useState({ isLoaded, error });

    useEffect(() => {
        if (isLoaded || error) {
            return;
        }

        if (!loadingPromise) {
            loadingPromise = new Promise((resolve, reject) => {
                // Check if Google Maps is already loaded
                if (window.google && window.google.maps) {
                    isLoaded = true;
                    resolve();
                    return;
                }

                // Check if the script is already in the DOM
                let script = document.querySelector('script[src*="maps.googleapis.com"]');

                if (!script) {
                    script = document.createElement('script');
                    script.id = SCRIPT_ID;
                    script.src = `https://maps.googleapis.com/maps/api/js?key=${API_KEY}&libraries=${LIBRARIES.join(',')}&loading=async`;
                    script.async = true;
                    script.defer = true;
                    document.head.appendChild(script);
                }

                const onLoad = () => {
                    isLoaded = true;
                    resolve();
                };

                const onError = (e) => {
                    error = new Error('Google Maps script failed to load.');
                    console.error(e);
                    reject(error);
                };

                if (script.complete || window.google && window.google.maps) {
                    onLoad();
                } else {
                    script.addEventListener('load', onLoad);
                    script.addEventListener('error', onError);
                }
            });
        }

        loadingPromise
            .then(() => {
                isLoaded = true;
                setState({ isLoaded: true, error: null });
            })
            .catch((err) => {
                error = err;
                setState({ isLoaded: false, error: err });
            });

    }, []);

    return state;
};

export default useGoogleMaps;
