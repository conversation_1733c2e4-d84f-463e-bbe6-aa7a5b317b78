@echo off
echo Setting up Android build environment...

REM Set JAVA_HOME to Android Studio's bundled JDK
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set PATH=%JAVA_HOME%\bin;%PATH%

REM Set Android SDK path
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set ANDROID_SDK_ROOT=%ANDROID_HOME%
set PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%PATH%

echo JAVA_HOME: %JAVA_HOME%
echo ANDROID_HOME: %ANDROID_HOME%

REM Verify Java installation
echo Checking Java version...
java -version

echo.
echo Running Gradle build...
gradlew.bat assembleDebug

pause
