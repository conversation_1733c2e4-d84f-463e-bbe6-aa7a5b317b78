const https = require('https');
const fs = require('fs');
const path = require('path');

// Create a simple self-signed certificate for localhost
const cert = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTIwOTEyMjE1MjAyWhcNMTUwOTEyMjE1MjAyWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAwQK1dMCbLZU9ApVZgZfPD6OBNQ2oxLqqyMCaWNkZ2RtPa9a4V9PtUOyE
qJsMhvj1eLkpVLWZOAqnlF6SvM6rQjVKX8EEKbrtKWisKQDdVjKs9x8UA2AlYOLy
VrQjGGym+a7+UEWOjo2OJbukJU/vMRRcvbvsnu7R5ZpVoKOLt1+uFVdvzK0E7TqR
lFOjQBmzIpVSTqJU+VdDQL7I8+uDH4KSAHS7afKqhwJzDTTx25CkvtxpdCJzg0Qw
rJWrqQqEjVS4hcKs4VkSqNL/XQKJeOTt3QBcg1SgDpKVVRtbMQKBgQDGGGym+a7+
UEWOjo2OJbukJU/vMRRcvbvsnu7R5ZpVoKOLt1+uFVdvzK0E7TqRlFOjQBmzIpVS
TqJU+VdDQL7I8+uDH4KSAHS7afKqhwJzDTTx25CkvtxpdCJzg0QwrJWrqQqEjVS4
hcKs4VkSqNL/XQKJeOTt3QBcg1SgDpKVVRtbMQKBgQDGGGym+a7+UEWOjo2OJbuk
JU/vMRRcvbvsnu7R5ZpVoKOLt1+uFVdvzK0E7TqRlFOjQBmzIpVSTqJU+VdDQL7I
8+uDH4KSAHS7afKqhwJzDTTx25CkvtxpdCJzg0QwrJWrqQqEjVS4hcKs4VkSqNL/
XQKJeOTt3QBcg1SgDpKVVRtbMQKBgQDGGGym+a7+UEWOjo2OJbukJU/vMRRcvbvs
nu7R5ZpVoKOLt1+uFVdvzK0E7TqRlFOjQBmzIpVSTqJU+VdDQL7I8+uDH4KSAHS7
afKqhwJzDTTx25CkvtxpdCJzg0QwrJWrqQqEjVS4hcKs4VkSqNL/XQKJeOTt3QBc
g1SgDpKVVRtbM
-----END CERTIFICATE-----`;

const key = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDBArV0wJstlT0C
lVmBl88Po4E1DajEuqrIwJpY2RnZG09r1rhX0+1Q7ISomwyG+PV4uSlUtZk4CqeU
XpK8zqtCNUpfwQQpuu0paKwpAN1WMqz3HxQDYCVg4vJWtCMYbKb5rv5QRY6OjY4l
u6QlT+8xFFy9u+ye7tHlmlWgo4u3X64VV2/MrQTtOpGUU6NAGbMilVJOolT5V0NA
vsjz64MfgpIAdLtp8qqHAnMNNPHbkKS+3Gl0InODRDCslauqCoSNVLiFwqzhWRKo
0v9dAol45O3dAFyDVKAOkpVVG1sxAoGBAMYYbKb5rv5QRY6OjY4lu6QlT+8xFFy9
u+ye7tHlmlWgo4u3X64VV2/MrQTtOpGUU6NAGbMilVJOolT5V0NAvsjz64MfgpIA
dLtp8qqHAnMNNPHbkKS+3Gl0InODRDCslauqCoSNVLiFwqzhWRKo0v9dAol45O3d
AFyDVKAOkpVVG1sxAoGBAMYYbKb5rv5QRY6OjY4lu6QlT+8xFFy9u+ye7tHlmlWg
o4u3X64VV2/MrQTtOpGUU6NAGbMilVJOolT5V0NAvsjz64MfgpIAdLtp8qqHAnMN
NPHbkKS+3Gl0InODRDCslauqCoSNVLiFwqzhWRKo0v9dAol45O3dAFyDVKAOkpVV
G1sxAoGBAMYYbKb5rv5QRY6OjY4lu6QlT+8xFFy9u+ye7tHlmlWgo4u3X64VV2/M
rQTtOpGUU6NAGbMilVJOolT5V0NAvsjz64MfgpIAdLtp8qqHAnMNNPHbkKS+3Gl0
InODRDCslauqCoSNVLiFwqzhWRKo0v9dAol45O3dAFyDVKAOkpVVG1sx
-----END PRIVATE KEY-----`;

const options = {
    key: key,
    cert: cert
};

// Simple static file server
const server = https.createServer(options, (req, res) => {
    let filePath = path.join(__dirname, req.url === '/' ? 'index.html' : req.url);

    // Remove query parameters for file path
    filePath = filePath.split('?')[0];

    // Security: prevent directory traversal
    if (filePath.includes('..')) {
        res.writeHead(403);
        res.end('Forbidden');
        return;
    }

    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404);
            res.end('Not Found');
            return;
        }

        // Set content type based on file extension
        const ext = path.extname(filePath);
        const contentTypes = {
            '.html': 'text/html',
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon'
        };

        const contentType = contentTypes[ext] || 'text/plain';
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
});

const PORT = 8443;
server.listen(PORT, () => {
    console.log(`🔒 HTTPS Server running at https://localhost:${PORT}`);
    console.log('⚠️  You may need to accept the self-signed certificate in your browser');
});
