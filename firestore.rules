rules_version = '2';

// =====================================================================
// CLOUD FIRESTORE RULES - Enhanced with Security Validation
// =====================================================================
service cloud.firestore {
  match /databases/{database}/documents {
    // --- EXISTING RULES (Keep these) ---
    match /artifacts/aura-global-app/public/data/activities/{document} {
      allow read: if true;
    }

    // --- JOURNEY RULES (ENHANCED WITH VALIDATION) ---
    match /journeys/{journeyId} {
      // Only authenticated users can create journeys with proper ownership and validation
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.creatorId
        && isValidJourneyData(request.resource.data);

      // Allow anyone to read a journey's location (for sharing)
      allow read: if true;

      // Only allow the journey creator to update or delete with validation
      allow update: if request.auth != null
        && resource.data.creatorId == request.auth.uid
        && isValidJourneyData(request.resource.data);

      allow delete: if request.auth != null
        && resource.data.creatorId == request.auth.uid;
    }

    // --- USER RATE LIMITING TRACKING FOR JOURNEYS ---
    match /users/{userId}/rateLimiting/lastJourneyTime {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // --- USER RATE LIMITING TRACKING FOR FILE UPLOADS ---
    match /users/{userId}/rateLimiting/lastUploadTime {
      allow write: if request.auth != null
        && request.auth.uid == userId
        && request.resource.data.keys().hasAll(['timestamp']);
    }

    match /artifacts/aura-global-app/public/{document=**} {
      allow read: if true;
    }

    // --- CODEX RULES ---
    match /codexes/{codexId}/{document=**} {
      allow read: if true;
      allow write: if request.auth != null && isValidCodexData(request.resource.data);
    }

    // --- USER DATA (ENHANCED WITH VALIDATION) ---
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null
        && request.auth.uid == userId
        && isValidUserData(request.resource.data);

      match /redeemed_coupons/{couponId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /journey_progress/{journeyId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // --- CHAT RULES (SECURED WITH SIMPLIFIED RATE LIMITING) ---
    match /artifacts/aura-global-app/public/data/aura-chat-rooms/{activityId}/messages/{messageId} {
      // Allow authenticated users to read messages
      allow read: if request.auth != null;

      // Allow authenticated users to write with validation but simplified rate limiting
      allow write: if request.auth != null
        && request.auth.uid == request.resource.data.senderId
        && request.resource.data.keys().hasAll(['senderId', 'senderName', 'timestamp'])
        && request.resource.data.senderId is string
        && request.resource.data.senderName is string
        && isValidMessageData(request.resource.data);
    }

    // --- USER RATE LIMITING TRACKING FOR MESSAGES ---
    match /users/{userId}/rateLimiting/lastMessageTime {
      allow read, write: if request.auth != null
        && request.auth.uid == userId;
    }

    // --- PRIVATE CHATS (SECURED) ---
    match /artifacts/aura-global-app/public/data/aura-private-chats/{chatId}/messages/{messageId} {
      // Validate chatId format and user participation
      allow read, write: if request.auth != null
        && request.auth.uid in chatId.split('_')
        && chatId.matches('^[a-zA-Z0-9]+_[a-zA-Z0-9]+$')
        && (request.method == 'get' || isValidMessageData(request.resource.data));
    }

    // --- DIRECTORY (READ-ONLY) ---
    match /artifacts/aura-global-app/public/data/directory/{document} {
      allow read: if true;
      allow write: if false; // Only backend can write
    }

    // --- ENHANCED VALIDATION FUNCTIONS ---
    function isValidUserData(data) {
      return data != null
        && (!data.keys().hasAny(['name']) || (data.name is string && data.name.size() <= 100))
        && (!data.keys().hasAny(['email']) || (data.email is string && data.email.matches('.*@.*\\\\..*')))
        && (!data.keys().hasAny(['emergencyContact']) || (data.emergencyContact is string && data.emergencyContact.size() <= 50));
    }

    function isValidJourneyData(data) {
      return data != null
        && data.keys().hasAll(['creatorId'])
        && data.creatorId is string
        && (!data.keys().hasAny(['title']) || (data.title is string && data.title.size() <= 200))
        && (!data.keys().hasAny(['description']) || (data.description is string && data.description.size() <= 1000));
    }

    function isValidCodexData(data) {
      return data != null
        && data.keys().hasAll(['title', 'description', 'region'])
        && data.title is string && data.title.size() <= 200
        && data.description is string && data.description.size() <= 5000
        && data.region is string && data.region.size() <= 100;
    }

    function isValidMessageData(data) {
      return data != null
        && data.keys().hasAll(['senderId', 'senderName', 'timestamp'])
        && data.senderId is string
        && data.senderName is string && data.senderName.size() <= 100
        && ((data.keys().hasAny(['text']) && data.text is string && data.text.size() <= 2000)
            || (data.keys().hasAny(['fileURL']) && data.fileURL is string));
    }

    function isWithinRateLimit() {
      return resource == null || request.time > resource.data.timestamp + duration.value(1, 'm');
    }
  }
}