# Android Development Environment Setup Script
Write-Host "Setting up Android development environment..." -ForegroundColor Green

# Set JAVA_HOME to Android Studio's bundled JDK
$env:JAVA_HOME = "C:\Program Files\Android\Android Studio\jbr"
Write-Host "JAVA_HOME set to: $env:JAVA_HOME" -ForegroundColor Green

# Set Android SDK paths
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = $env:ANDROID_HOME
Write-Host "ANDROID_HOME set to: $env:ANDROID_HOME" -ForegroundColor Green

# Update PATH
$env:PATH = "$($env:JAVA_HOME)\bin;$($env:ANDROID_HOME)\platform-tools;$($env:ANDROID_HOME)\tools;$($env:PATH)"
Write-Host "PATH updated with Java and Android tools" -ForegroundColor Green

# Verify Java installation
Write-Host "`nVerifying Java installation..." -ForegroundColor Yellow
try {
    java -version
    Write-Host "Java is working correctly!" -ForegroundColor Green
} catch {
    Write-Host "Java verification failed!" -ForegroundColor Red
    exit 1
}

# Set environment variables permanently (optional)
Write-Host "`nSetting environment variables permanently..." -ForegroundColor Yellow
try {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Android\Android Studio\jbr", "User")
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", "C:\Users\<USER>\AppData\Local\Android\Sdk", "User")
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", "C:\Users\<USER>\AppData\Local\Android\Sdk", "User")

    Write-Host "Environment variables set permanently!" -ForegroundColor Green
    Write-Host "You may need to restart your terminal/IDE for permanent changes to take effect." -ForegroundColor Yellow
} catch {
    Write-Host "Could not set permanent environment variables. Using session variables only." -ForegroundColor Yellow
}

Write-Host "`nAndroid environment setup complete!" -ForegroundColor Green
Write-Host "You can now run: npm run cap:run:android" -ForegroundColor Cyan
