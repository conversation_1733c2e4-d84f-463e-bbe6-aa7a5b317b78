const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const distDir = path.join(__dirname, 'dist');

// 1. Clean the dist directory
console.log('🧹 Cleaning dist directory...');
if (fs.existsSync(distDir)) {
    let retries = 5;
    while (retries > 0) {
        try {
            fs.rmSync(distDir, { recursive: true, force: true });
            console.log('✅ Dist directory cleaned.');
            break; // Succeeded
        } catch (err) {
            if (err.code === 'EBUSY' && retries > 1) {
                console.warn(`⚠️ Dist directory is busy. Retrying in 1 second... (${retries - 1} retries left)`);
                retries--;
                // Synchronous 1-second delay
                const end = new Date().getTime() + 1000;
                while (new Date().getTime() < end) { /* wait */ }
            } else {
                console.error('❌ Failed to clean dist directory. It might be locked by another process.');
                throw err; // Re-throw on last attempt or for other errors
            }
        }
    }
}
fs.mkdirSync(distDir, { recursive: true });

// 2. Generate secure configuration
console.log('🔧 Generating secure configuration...');
try {
    // We call the original config.js script with node to generate the browser-safe config in dist
    execSync('node config.js', { stdio: 'inherit' });
} catch (error) {
    console.error('❌ Failed to generate configuration:', error.message);
    process.exit(1);
}

// 3. Build CSS
console.log('🎨 Building CSS...');
try {
    // Copy base styles
    fs.copyFileSync(path.join(__dirname, 'src', 'style.css'), path.join(distDir, 'style.css'));
    fs.copyFileSync(path.join(__dirname, 'src', 'solo.css'), path.join(distDir, 'solo.css'));
    // Build Tailwind CSS
    execSync('npx postcss src/input.css --config postcss.prod.config.js -o dist/tailwind.css', { stdio: 'inherit' });
    console.log('✅ CSS build complete.');
} catch (error) {
    console.error('❌ Failed to build CSS:', error.message);
    process.exit(1);
}

// 4. Build JavaScript with esbuild
console.log('📦 Bundling JavaScript with esbuild...');
try {
    const esbuildCmds = [
        'npx esbuild app.js --bundle --outfile=dist/app.js --jsx-factory=React.createElement --jsx-fragment=React.Fragment',
        'npx esbuild src/features/codex/index.jsx --bundle --outfile=dist/codex.js --jsx-factory=React.createElement --jsx-fragment=React.Fragment --format=iife --global-name=CodexApp',
        'npx esbuild journey.js --bundle --outfile=dist/journey.js',
        'npx esbuild src/features/codex/userGuide.js --bundle --outfile=dist/userGuide.js',
            'npx esbuild src/security/csp-config.js --bundle --outfile=dist/csp-config.js',
            'npx esbuild adsense-loader.js --bundle --outfile=dist/adsense-loader.js'
    ];
    esbuildCmds.forEach(cmd => execSync(cmd, { stdio: 'inherit' }));
    console.log('✅ JavaScript bundling complete.');
} catch (error) {
    console.error('❌ Failed to bundle JavaScript:', error.message);
    process.exit(1);
}

// 5. Copy static assets
console.log('🚚 Copying static assets...');
const staticAssets = [
    'index.html',
    'journey.html',
    'ads.txt',
    'manifest.json',
    'sw.js',
    'icons',
    'resources',
    'assets'
];
staticAssets.forEach(asset => {
    const sourcePath = path.join(__dirname, asset);
    const destPath = path.join(distDir, asset);
    if (fs.existsSync(sourcePath)) {
        const stat = fs.lstatSync(sourcePath);
        if (stat.isDirectory()) {
            fs.mkdirSync(destPath, { recursive: true });
            fs.readdirSync(sourcePath).forEach(file => {
                fs.copyFileSync(path.join(sourcePath, file), path.join(destPath, file));
            });
        } else {
            fs.copyFileSync(sourcePath, destPath);
        }
        console.log(`✅ Copied ${asset}`);
    } else {
        console.warn(`⚠️ Asset not found, skipping: ${asset}`);
    }
});

// 6. Fix paths in HTML files
console.log('🔧 Fixing paths in HTML files...');
const htmlFiles = ['index.html', 'journey.html'];
htmlFiles.forEach(fileName => {
    const filePath = path.join(distDir, fileName);
    if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Remove old script tags that are now bundled
        content = content.replace(/<script src="\.\/src\/security\/.*?\.js"><\/script>/g, '');
        content = content.replace(/<script src="config\.js"><\/script>/g, '<script src="./config.js"></script>');

        // Fix CSS paths
        content = content.replace(/href="\.\/src\/style\.css"/g, 'href="./style.css"');
        content = content.replace(/href="\.\/src\/solo\.css"/g, 'href="./solo.css"');
        content = content.replace(/href="style\.css"/g, 'href="./style.css"');
        content = content.replace(/href="solo\.css"/g, 'href="./solo.css"');
        content = content.replace(/href="tailwind\.css"/g, 'href="./tailwind.css"');
        
        // Fix JS paths
        content = content.replace(/src="app\.js"/g, 'src="./app.js"');
        content = content.replace(/src="codex\.js"/g, 'src="./codex.js"');
        content = content.replace(/src="journey\.js"/g, 'src="./journey.js"');

        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed paths in ${fileName}`);
    }
});

// 7. Sync with Capacitor
console.log('🔄 Syncing with Capacitor for Android...');
try {
    execSync('npx cap sync android', { stdio: 'inherit' });
    console.log('✅ Capacitor sync complete for Android.');
} catch (error) {
    console.error('❌ Failed to sync with Capacitor:', error.message);
    process.exit(1);
}

console.log('🎉 Build and sync complete! The Android project is updated.');
