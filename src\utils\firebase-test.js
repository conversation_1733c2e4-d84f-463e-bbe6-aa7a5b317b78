/**
 * Firebase Connection Test for Android
 * This utility helps test Firebase connectivity on both web and native platforms
 */

import { isNativePlatform } from './platform-utils.js';

export const testFirebaseConnection = async () => {
    console.log('🔥 Testing Firebase connection...');
    console.log('Platform:', isNativePlatform() ? 'Native Android' : 'Web/PWA');
    
    try {
        // Test Firebase initialization
        if (window.auraFirebase && window.auraFirebase.db) {
            console.log('✅ Firebase database instance found');
            
            // Test Firestore connection
            const { collection, getDocs } = window.auraFirebase;
            
            // Try to read from a simple collection (this will create it if it doesn't exist)
            const testCollection = collection(window.auraFirebase.db, 'connection-test');
            const snapshot = await getDocs(testCollection);
            
            console.log('✅ Firestore connection successful');
            console.log(`📊 Test collection has ${snapshot.size} documents`);
            
            // Test Auth
            if (window.auraFirebase.auth) {
                console.log('✅ Firebase Auth instance found');
                console.log('👤 Current user:', window.auraFirebase.auth.currentUser ? 'Signed in' : 'Anonymous');
            }
            
            return {
                success: true,
                platform: isNativePlatform() ? 'native' : 'web',
                firestore: true,
                auth: !!window.auraFirebase.auth,
                message: 'Firebase connection successful'
            };
            
        } else {
            throw new Error('Firebase not initialized');
        }
        
    } catch (error) {
        console.error('❌ Firebase connection failed:', error);
        
        return {
            success: false,
            platform: isNativePlatform() ? 'native' : 'web',
            error: error.message,
            message: 'Firebase connection failed'
        };
    }
};

// Auto-test on load (only in development)
if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
    // Wait for Firebase to initialize
    setTimeout(() => {
        testFirebaseConnection().then(result => {
            console.log('🔥 Firebase Test Result:', result);
        });
    }, 3000);
}

// Make it available globally for manual testing
window.testFirebaseConnection = testFirebaseConnection;
