/**
 * Content Security Policy Configuration
 * Provides CSP headers and meta tag generation for XSS protection
 */

class CSPConfig {
    constructor() {
        this.initializeCSP();
    }

    /**
     * Get CSP directives based on environment
     */
    getCSPDirectives() {
        const baseDirectives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'", // Required for inline scripts - consider removing in production
                "https://www.gstatic.com",
                "https://maps.googleapis.com",
                "https://cdn.tailwindcss.com",
                "https://pagead2.googlesyndication.com",
                "https://www.google-analytics.com",
                "https://ssl.google-analytics.com",
                "https://tagmanager.google.com",
                "https://googletagmanager.com",
                "https://apis.google.com",
                "https://ep2.adtrafficquality.google",
                // AdMob/Ads related
                "https://googleads.g.doubleclick.net",
                "https://tpc.googlesyndication.com",
                "https://adservice.google.com",
                "https://adservice.google.com.au",
                "https://fundingchoicesmessages.google.com"
            ],
            'script-src-elem': [
                "'self'",
                "https://www.gstatic.com",
                "https://maps.googleapis.com",
                "https://cdn.tailwindcss.com",
                "https://pagead2.googlesyndication.com",
                "https://www.google-analytics.com",
                "https://ssl.google-analytics.com",
                "https://tagmanager.google.com",
                "https://www.googletagmanager.com",
                "https://apis.google.com",
                "https://ep2.adtrafficquality.google",
                // AdMob/Ads related
                "https://googleads.g.doubleclick.net",
                "https://tpc.googlesyndication.com",
                "https://adservice.google.com",
                "https://adservice.google.com.au",
                "https://fundingchoicesmessages.google.com"
            ],
            'worker-src': [
                "'self'",
                "blob:"
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'", // Required for inline styles
                "https://fonts.googleapis.com",
                "https://cdn.tailwindcss.com"
            ],
            'font-src': [
                "'self'",
                "https://fonts.gstatic.com"
            ],
            'img-src': [
                "'self'",
                "data:",
                "https:",
                "blob:",
                "https://pagead2.googlesyndication.com",
                "https://www.google-analytics.com",
                "https://www.google.com",
                "https://www.google.com.au",
                // Ads rendering images
                "https://tpc.googlesyndication.com",
                "https://googleads.g.doubleclick.net"
            ],
            'connect-src': [
                "'self'",
                "https://firebaseapp.com",
                "https://*.firebaseapp.com",
                "https://firestore.googleapis.com",
                "https://firebase.googleapis.com",
                "https://securetoken.googleapis.com",
                "https://maps.googleapis.com",
                "https://www.google.com",
                "https://www.google.com.au",
                "https://pagead2.googlesyndication.com",
                "https://www.google-analytics.com",
                "https://ep1.adtrafficquality.google",
                "https://firebaseinstallations.googleapis.com",
                "https://identitytoolkit.googleapis.com",
                // AdMob/Ads requests
                "https://googleads.g.doubleclick.net",
                "https://tpc.googlesyndication.com",
                "https://adservice.google.com",
                "https://adservice.google.com.au",
                "https://fundingchoicesmessages.google.com"
            ],
            'frame-src': [
                "'self'",
                "https://googleads.g.doubleclick.net",
                "https://pagead2.googlesyndication.com",
                "https://aura-app-backend.firebaseapp.com",
                "https://ep2.adtrafficquality.google",
                "https://www.google.com",
                "https://www.google.com.au",
                // Funding choices / consent dialogs
                "https://fundingchoicesmessages.google.com"
            ],
            'object-src': [
                "'none'"
            ],
            'base-uri': [
                "'self'"
            ],
            'form-action': [
                "'self'"
            ]
        };

        // Environment-specific adjustments
        if (this.environment === 'development') {
            // Allow localhost for development
            baseDirectives['connect-src'].push('http://localhost:*', 'ws://localhost:*');
            baseDirectives['script-src'].push('http://localhost:*');
            baseDirectives['style-src'].push('http://localhost:*');
        }

        return baseDirectives;
    }

    /**
     * Generate CSP header string
     */
    generateCSPHeader() {
        const directives = this.getCSPDirectives();
        const cspParts = [];

        for (const [directive, sources] of Object.entries(directives)) {
            cspParts.push(`${directive} ${sources.join(' ')}`);
        }

        return cspParts.join('; ');
    }

    /**
     * Generate CSP meta tag
     */
    generateCSPMetaTag() {
        const cspHeader = this.generateCSPHeader();
        return `<meta http-equiv="Content-Security-Policy" content="${cspHeader}">`;
    }

    /**
     * Initialize CSP protection
     */
    initializeCSP() {
        // Add CSP meta tag if not already present
        if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
            this.addCSPMetaTag();
        }

        // Log CSP initialization
        console.log('🛡️ CSP Protection initialized for environment:', this.environment);
        
        // Monitor CSP violations
        this.setupCSPViolationReporting();
    }

    /**
     * Add CSP meta tag to document head
     */
    addCSPMetaTag() {
        const metaTag = document.createElement('meta');
        metaTag.setAttribute('http-equiv', 'Content-Security-Policy');
        metaTag.setAttribute('content', this.generateCSPHeader());
        
        // Insert after charset meta tag if it exists
        const charsetMeta = document.querySelector('meta[charset]');
        if (charsetMeta && charsetMeta.nextSibling) {
            charsetMeta.parentNode.insertBefore(metaTag, charsetMeta.nextSibling);
        } else {
            document.head.insertBefore(metaTag, document.head.firstChild);
        }
    }

    /**
     * Setup CSP violation reporting
     */
    setupCSPViolationReporting() {
        document.addEventListener('securitypolicyviolation', (event) => {
            this.handleCSPViolation(event);
        });
    }

    /**
     * Handle CSP violations
     */
    handleCSPViolation(event) {
        const violation = {
            blockedURI: event.blockedURI,
            violatedDirective: event.violatedDirective,
            originalPolicy: event.originalPolicy,
            sourceFile: event.sourceFile,
            lineNumber: event.lineNumber,
            columnNumber: event.columnNumber,
            timestamp: new Date().toISOString()
        };

        // Log violation
        console.warn('🚨 CSP Violation detected:', violation);

        // Store violation for monitoring
        this.storeCSPViolation(violation);

        // In development, show detailed violation info
        if (this.environment === 'development') {
            this.showCSPViolationNotification(violation);
        }
    }

    /**
     * Store CSP violation for monitoring
     */
    storeCSPViolation(violation) {
        try {
            const violations = JSON.parse(localStorage.getItem('cspViolations') || '[]');
            violations.push(violation);
            
            // Keep only last 50 violations
            if (violations.length > 50) {
                violations.splice(0, violations.length - 50);
            }
            
            localStorage.setItem('cspViolations', JSON.stringify(violations));
        } catch (error) {
            console.warn('Could not store CSP violation:', error);
        }
    }

    /**
     * Show CSP violation notification in development
     */
    showCSPViolationNotification(violation) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: #dc2626;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            font-size: 14px;
            font-family: system-ui, -apple-system, sans-serif;
        `;
        notification.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 4px;">🚨 CSP Violation</div>
            <div style="font-size: 12px;">
                <strong>Directive:</strong> ${violation.violatedDirective}<br>
                <strong>Blocked:</strong> ${violation.blockedURI}<br>
                <strong>Source:</strong> ${violation.sourceFile}:${violation.lineNumber}
            </div>
            <button onclick="this.parentElement.remove()" style="
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: white;
            ">×</button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 15 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 15000);
    }

    /**
     * Get CSP violation statistics
     */
    getCSPStats() {
        try {
            const violations = JSON.parse(localStorage.getItem('cspViolations') || '[]');
            const recentViolations = violations.filter(violation => 
                Date.now() - new Date(violation.timestamp).getTime() < 24 * 60 * 60 * 1000
            );
            
            return {
                totalViolations: violations.length,
                recentViolations: recentViolations.length,
                lastViolation: violations.length > 0 ? violations[violations.length - 1] : null,
                cspActive: true,
                environment: this.environment
            };
        } catch (error) {
            return {
                totalViolations: 0,
                recentViolations: 0,
                lastViolation: null,
                cspActive: true,
                environment: this.environment
            };
        }
    }

    /**
     * Clear stored violations
     */
    clearViolations() {
        localStorage.removeItem('cspViolations');
    }

    /**
     * Generate security headers for server configuration
     */
    generateSecurityHeaders() {
        return {
            'Content-Security-Policy': this.generateCSPHeader(),
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(self), microphone=(self), camera=(self)'
        };
    }
}

// Create global instance
window.CSPConfig = new CSPConfig();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSPConfig;
}
