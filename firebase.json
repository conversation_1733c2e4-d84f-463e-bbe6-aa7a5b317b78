{"hosting": [{"target": "live", "public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "geolocation=(self), microphone=(self), camera=(self)"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"target": "staging", "public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}]}