# Security Headers for Nginx
# Add these to your server block

add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "geolocation=(self), microphone=(self), camera=(self)" always;

# Force HTTPS
if ($scheme != "https") {
    return 301 https://$host$request_uri;
}