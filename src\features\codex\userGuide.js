document.addEventListener('DOMContentLoaded', () => {
    const setupGuideLink = (linkId, guideSectionId) => {
        const link = document.getElementById(linkId);
        if (link) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                // Navigate to the info screen
                document.getElementById('nav-info').click();
                // Show the user guide
                document.getElementById('info-user-guide-button').click();
                
                // Open the specific guide section
                setTimeout(() => {
                    const allDetails = document.querySelectorAll('#user-guide-screen details');
                    allDetails.forEach(details => {
                        details.open = false;
                    });
                    const guideSection = document.getElementById(guideSectionId);
                    if (guideSection) {
                        guideSection.open = true;
                        guideSection.scrollIntoView({ behavior: 'smooth' });
                    }
                }, 100); // A small delay to ensure the screen has transitioned
            });
        }
    };

    const setupDynamicLink = (linkId, guideSectionId) => {
        const observer = new MutationObserver((mutationsList, observer) => {
            const link = document.getElementById(linkId);
            if (link) {
                setupGuideLink(linkId, guideSectionId);
                observer.disconnect();
            }
        });

        observer.observe(document.body, { childList: true, subtree: true });
    };

    setupGuideLink('how-to-trip-planner', 'guide-trip-planner');
    setupGuideLink('how-to-activities', 'guide-activities');
    setupDynamicLink('how-to-journeys', 'guide-journeys');

    const allDetails = document.querySelectorAll('#user-guide-screen details');
    allDetails.forEach(details => {
        details.addEventListener('toggle', (e) => {
            if (details.open) {
                allDetails.forEach(d => {
                    if (d !== details) {
                        d.open = false;
                    }
                });
            }
        });
    });
});