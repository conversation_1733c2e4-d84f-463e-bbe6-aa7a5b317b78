/**
 * Configuration Builder for Monoloci App
 * Generates secure configuration from environment variables
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env file if it exists
function loadEnvFile() {
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envLines = envContent.split('\n');

        envLines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, ...valueParts] = trimmedLine.split('=');
                if (key && valueParts.length > 0) {
                    const value = valueParts.join('=').trim();
                    // Remove quotes if present
                    const cleanValue = value.replace(/^["']|["']$/g, '');
                    process.env[key.trim()] = cleanValue;
                }
            }
        });
    }
}

// Generate Firebase configuration
function generateFirebaseConfig() {
    loadEnvFile();

    const config = {
        apiKey: process.env.FIREBASE_API_KEY,
        authDomain: process.env.FIREBASE_AUTH_DOMAIN,
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.FIREBASE_APP_ID
    };

    // Validate required fields
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !config[field] || config[field].includes('your-'));

    if (missingFields.length > 0) {
        console.warn('⚠️  Warning: Missing or placeholder Firebase configuration for:', missingFields.join(', '));
        console.warn('   Please check your .env file or environment variables.');
    }

    return config;
}

// Generate Google Maps configuration
function generateMapsConfig() {
    loadEnvFile();

    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey || apiKey.includes('your-')) {
        console.warn('⚠️  Warning: Missing or placeholder Google Maps API key');
        console.warn('   Please set GOOGLE_MAPS_API_KEY in your .env file');
    }

    return {
        apiKey: apiKey
    };
}

// Generate configuration file for the app
function generateConfigFile() {
    const firebaseConfig = generateFirebaseConfig();
    const mapsConfig = generateMapsConfig();

    const configContent = `
// Auto-generated configuration file
// DO NOT EDIT MANUALLY - This file is generated by config.js

// Check if we're in a browser environment
if (typeof window !== 'undefined') {
    window.FIREBASE_CONFIG = ${JSON.stringify(firebaseConfig, null, 2)};
    window.GOOGLE_MAPS_CONFIG = ${JSON.stringify(mapsConfig, null, 2)};
    window.APP_CONFIG = {
        version: "${process.env.APP_VERSION || '1.3.0'}",
        name: "${process.env.APP_NAME || 'Monoloci Activity Companion'}"
    };

    console.log('🔧 Configuration loaded');
}
`;

    // Write to dist directory
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }

    const configPath = path.join(distDir, 'config.js');
    fs.writeFileSync(configPath, configContent.trim());

    console.log('✅ Configuration file generated:', configPath);
    return configPath;
}

// Export functions for use in build scripts
module.exports = {
    generateFirebaseConfig,
    generateMapsConfig,
    generateConfigFile,
    loadEnvFile
};

// Run if called directly
if (require.main === module) {
    generateConfigFile();
}