/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './index.html',
    './journey.html',
    './app.js',
    './journey.js',
    './codex.js',
    './src/**/*.{js,jsx,css}',
    './dist/**/*.{js,html}'
  ],
  safelist: [
    // Common layout classes
    'flex', 'flex-col', 'flex-row', 'grid', 'block', 'inline-block', 'hidden',
    // Spacing classes
    'p-1', 'p-2', 'p-3', 'p-4', 'p-6', 'p-8', 'm-1', 'm-2', 'm-3', 'm-4', 'm-6', 'm-8',
    'px-2', 'px-3', 'px-4', 'px-6', 'py-2', 'py-3', 'py-4', 'py-6',
    // Colors
    'bg-white', 'bg-gray-100', 'bg-gray-200', 'bg-blue-500', 'bg-green-500', 'bg-red-500',
    'text-white', 'text-gray-600', 'text-gray-800', 'text-blue-600',
    // Text
    'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl',
    'font-bold', 'font-medium', 'text-center',
    // Borders and rounded
    'border', 'border-gray-300', 'rounded', 'rounded-lg', 'rounded-full',
    // Width/Height
    'w-full', 'h-full', 'w-auto', 'h-auto',
    // Position
    'relative', 'absolute', 'fixed', 'sticky'
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
