<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Monoloci: Explore with Purpose</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="A tourist companion app for travel enthusiasts with emergency features, trip planner, journeys and real-time group chat">
    <meta name="keywords" content="monoloci, activity, safety, tourism, tour, guide, travel, companion, emergency, trip planner, mystery journeys, group chat, location sharing">
    <meta name="author" content="Monoloci">
    <meta name="theme-color" content="#0ea5e9">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Monoloci">
    <meta name="msapplication-TileColor" content="#0ea5e9">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- Security and Compatibility -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="no-referrer-when-downgrade">

    <!-- Permissions Policy - Handle Privacy Sandbox features -->
    <meta http-equiv="Permissions-Policy" content="
        private-state-token-redemption=(),
        private-state-token-issuance=(),
        browsing-topics=(),
        run-ad-auction=(),
        join-ad-interest-group=()
    ">

    <!-- The Content Security Policy is now loaded dynamically from csp-config.js -->

    <!-- Icons -->
    <link rel="icon" href="/icons/icon-192.png" type="image/png">
    <link rel="apple-touch-icon" href="/icons/icon-192.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192.png">
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">


    <!-- External Resources -->
    <!-- Use built Tailwind CSS instead of CDN -->
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./solo.css">
    <link rel="stylesheet" href="./tailwind.css">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Secure Configuration - Load before app scripts -->
    <script src="./config.js"></script>
    <script src="./csp-config.js"></script>

    <!-- Google Maps API with improved loading -->
    <!-- Google Maps API will be loaded dynamically by the app's components -->
</head>
<body>

    <!-- Main App Container -->
    <div id="app-container" class="fixed inset-0 bg-white flex flex-col">
        <!-- Scrollable Content Area -->
        <div class="flex-1 overflow-y-auto min-h-0">
            <!-- Home Screen -->
            <div id="home-screen" class="relative flex flex-col main-content h-full">
                <div id="home-bg" class="absolute inset-0 bg-cover bg-center z-0"></div>
                <div class="relative z-10 flex-1 flex flex-col">
                    <div class="text-center p-4 px-12">
                        <img src="./assets/monoloci_header.png" alt="Monoloci" class="w-full" />
                    </div>
                    
                    <div class="flex-1 flex flex-col justify-center items-center text-center p-6">
                        <div id="home-button-container" class="relative flex justify-center items-center">
                            <!-- Indented Triangle Background -->
                            <div id="home-triangle-bg" class="absolute inset-0"></div>
                    
                            <!-- SOS Button (Top of Triangle) -->
                            <button id="home-sos-button" class="absolute top-0 z-10 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 active:scale-95 transition-transform pulse-animation font-bold tracking-wider">SOS</button>
                            
                            <!-- Bottom Triangle Buttons -->
                            <div class="absolute bottom-0 z-10 flex justify-between w-full">
                                <button id="share-location-button-home" class="bg-sky-500 text-white rounded-full flex flex-col items-center justify-center hover:bg-sky-600 active:scale-95 transition-transform text-center font-semibold p-2">Share My<br>Journey</button>
                                <button id="sound-scape-button" class="bg-sky-500 text-white rounded-full flex flex-col items-center justify-center hover:bg-sky-600 active:scale-95 transition-transform text-center font-semibold p-2">Sound<br>Scape</button>
                            </div>
                        </div>
                        <p id="sound-scape-status" class="mt-4 h-6"></p>
                    </div>
                </div>
            </div>
            <!-- Solo Screen -->
            <div id="solo-screen" class="hidden flex flex-col main-content">
                <div class="container mx-auto p-4">
                    <h1 class="text-2xl font-bold text-slate-800 text-center mb-4">Trip Planner</h1>
                    <h5 class="text-1xl font-regular text-slate-600 text-center mb-4"><a href="#" id="how-to-trip-planner" class="text-sky-500 hover:underline">How to Use</a></h5>
                    <div id="map" class="w-full h-72 bg-slate-200 rounded-lg mb-4 shadow-md"></div>
                    <div id="trip-form" class="bg-white p-4 rounded-lg shadow-md mb-4">
                        <h2 class="text-xl font-semibold mb-2">Add new stop</h2>
                        <input type="text" id="locationName" placeholder="Attraction Name" required class="w-full p-2 border border-slate-300 rounded-md mb-2">
                        <input type="time" id="arrivalTime" placeholder="Arrival Time" class="w-full p-2 border border-slate-300 rounded-md mb-2">
                        <input type="text" id="duration" placeholder="Duration in hours (e.g., 2)" class="w-full p-2 border border-slate-300 rounded-md mb-2">
                        <button id="addStopBtn" class="w-full py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Add Stop</button>
                    </div>
                    <div id="trip-plan" class="bg-white p-4 rounded-lg shadow-md">
                        <h2 class="text-xl font-semibold mb-2">Your Itinerary</h2>
                        <div class="overflow-x-auto">
                            <table id="stopsTable" class="w-full text-sm text-left text-slate-500">
                                <thead class="text-xs text-slate-700 uppercase bg-slate-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3">Stop</th>
                                        <th scope="col" class="px-6 py-3">Location Name</th>
                                        <th scope="col" class="px-6 py-3">Arrival Time</th>
                                        <th scope="col" class="px-6 py-3">Duration (hrs)</th>
                                        <th scope="col" class="px-6 py-3">Travel to Next</th>
                                        <th scope="col" class="px-6 py-3">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Stops will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button id="calculateTravelBtn" class="w-full py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Calculate Travel</button>
                            <button id="analyzeBtn" class="w-full py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600" disabled>Analyze Itinerary</button>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button id="clearTripBtn" class="w-full py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700">Clear Trip/Map</button>
                            <button id="getDirectionsBtn" class="w-full py-2 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700" disabled>Get Directions</button>
                        </div>
                        <div class="flex space-x-2 mt-4">
                            <button id="saveItineraryBtn" class="w-full py-2 bg-slate-500 text-white font-semibold rounded-lg hover:bg-slate-600">Save Itinerary</button>
                            <button id="exportItineraryBtn" class="w-full py-2 bg-slate-500 text-white font-semibold rounded-lg hover:bg-slate-600">Export Itinerary</button>
                        </div>
                        <div id="travel-summary" class="mt-4"></div>
                        <div id="analysis-results" class="mt-4"></div>
                    </div>
                    <div id="saved-itineraries" class="bg-white p-4 rounded-lg shadow-md mt-4">
                        <h2 class="text-xl font-semibold mb-2">Saved Itineraries</h2>
                        <ul id="saved-itineraries-list" class="space-y-2">
                            <!-- Saved itineraries will be listed here -->
                        </ul>
                    </div>
                </div>
            </div>
            <!-- Group Screen -->
            <div id="group-screen" class="hidden flex flex-col main-content">
                <div class="p-3 pt-6 border-b">
                    <h1 class="text-2xl font-bold text-slate-800 text-center">Group Activities</h1>
                    <h5 class="text-1xl font-regular text-slate-600 text-center mb-4">Got an Activity Code? Enter it below. <a href="#" id="how-to-activities" class="text-sky-500 hover:underline">How to Use</a></h5>
                </div>

                <div class="p-4 border-b bg-slate-50">
                    <form id="access-form" class="w-full max-w-sm mx-auto mt-3">
                        <div class="flex gap-2">
                            <input type="text" id="access-code-input" placeholder="Enter Code" autocomplete="off" class="flex-1 px-4 py-3 text-center tracking-widest font-bold bg-slate-100 border rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500">
                            <button type="submit" class="px-6 py-3 bg-sky-500 text-white rounded-lg font-semibold hover:bg-sky-600 active:scale-95">Join</button>
                        </div>
                        <p id="error-message" class="text-red-500 mt-2 h-5 text-sm text-center"></p>
                    </form>
                </div>

                <div id="itinerary-list-container" class="flex-1 p-4 space-y-3 overflow-y-auto">
                    <h2 class="text-lg font-semibold text-slate-700 text-center">My Activities</h2>
                    <div id="itinerary-list"></div>
                </div>

                <div class="px-4 pt-4">
                    <hr class="border-slate-300">
                </div>

                <div class="p-4">
                    <h2 class="text-lg font-semibold text-slate-700 mb-2 text-center">Explore Activities</h2>
                    <img src="./assets/activity-header.webp" alt="Directory Header" class="w-full h-24 object-cover rounded-lg">
                </div>


                <!-- Region Selector -->
                <div class="p-4 bg-slate-100 border-t border-b">
                    <label for="region-select" class="block text-sm font-medium text-slate-700 mb-1">Explore Activities by Region</label>
                    <select id="region-select" class="w-full p-2 border border-slate-300 rounded-md">
                        <option value="">Select a Region</option>
                        <option value="New South Wales">New South Wales</option>
                        <option value="Victoria">Victoria</option>
                        <option value="Queensland">Queensland</option>
                        <option value="Western Australia">Western Australia</option>
                        <option value="South Australia">South Australia</option>
                        <option value="Tasmania">Tasmania</option>
                        <option value="Australian Capital Territory">Australian Capital Territory</option>
                        <option value="Northern Territory">Northern Territory</option>
                    </select>
                </div>

                <!-- Directory List -->
                <div id="directory-list" class="flex-1 p-4 space-y-3 overflow-y-auto"></div>
            </div>
            <!-- Info Screen -->
            <div id="info-screen" class="hidden flex flex-col main-content">
                <div class="p-3 pt-6 border-b">
                    <h1 class="text-2xl font-bold text-slate-800 text-center">Information</h1>
                </div>
                <div class="p-4 space-y-3">
                    <button id="info-sos-settings-button" class="w-full text-left p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-red-500"><path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/></svg>
                        <div>
                            <p class="font-bold text-slate-800">SOS Settings</p>
                            <p class="text-sm text-slate-600">Manage your emergency contact and message.</p>
                        </div>
                    </button>
                    <button id="info-user-guide-button" class="w-full text-left p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-sky-500"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                        <div>
                            <p class="font-bold text-slate-800">App User Guide</p>
                            <p class="text-sm text-slate-600">Learn how to use the Monoloci app.</p>
                        </div>
                    </button>
                    <button id="info-sponsors-button" class="w-full text-left p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-amber-500"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>
                        <div>
                            <p class="font-bold text-slate-800">Sponsors</p>
                            <p class="text-sm text-slate-600">View our partners.</p>
                            </div>
                            </button>
                            <button id="info-notes-button" class="w-full text-left p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-indigo-500"><path d="M15.5 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3z"/><path d="M15 3v6h6"/></svg>
                                <div>
                                    <p class="font-bold text-slate-800">My Notes</p>
                                    <p class="text-sm text-slate-600">Packing lists, interests, and documents.</p>
                                </div>
                            </button>
                            <button id="install-pwa-button" class="hidden w-full text-left p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-green-500"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>
                                <div>
                                    <p class="font-bold text-slate-800">Install App</p>
                                    <p class="text-sm text-slate-600">Get the best experience on your device.</p>
                                </div>
                            </button>
                            </div>

                            <!-- Ad Container -->
                            <div class="flex-1 flex items-center justify-center p-4">
                                <div id="adsense-container">
                            <!-- AdSense will be loaded here -->
                                </div>
                            </div>
                            </div>
            <!-- User Guide Screen -->
            <div id="user-guide-screen" class="hidden flex flex-col main-content">
                <div class="p-3 border-b border-slate-200 bg-slate-50 text-center flex items-center">
                    <button id="guide-back-to-info" class="bg-slate-200 text-slate-700 py-1 px-3 rounded-md hover:bg-slate-300 transition-colors">&larr; Back</button>
                    <div class="flex-1"><h1 class="text-xl font-bold text-slate-800">User Guide</h1></div><div class="w-10"></div>
                </div>
                <div class="p-6 text-slate-700 leading-relaxed">
                    <h2 class="text-2xl font-bold text-slate-800 mb-4">Welcome to Monoloci</h2>
                    <p class="mb-4">Monoloci is your personal companion for Trip planning, Activities and Journeys, designed to keep you safe, connected and on-time. Here’s a complete guide to all the features.</p>
                    
                    <details id="guide-sos" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">SOS</summary>
                        <div class="p-4">
                            <p>The <strong class="text-sky-600">Home</strong> screen provides immediate access to your most critical safety tools.</p>
                            <ul class="list-disc list-inside mt-2 space-y-2">
                                <li><strong>SOS Button:</strong> This is your primary emergency control. Depending on your settings, it will either instantly prepare an SMS to your emergency contact or open an options menu. If the menu appears, you have 2 choices:
                                    <ul class="list-['-_'] list-inside ml-4 mt-1 space-y-1">
                                        <li><strong>Call Emergency (000):</strong> This will open your phone's dialer with the emergency number pre-filled. <strong class="text-red-600">You still need to press the call button to connect.</strong></li>
                                        <li><strong>Alert Emergency Contact:</strong> This prepares an SMS to your nominated contact with your custom message and location URL. <strong class="text-red-600">You still need to press "send" in your messaging app.</strong></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-share-journey" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Share My Journey</summary>
                        <div class="p-4">
                             <ul class="list-disc list-inside mt-2 space-y-2">
                                <li><strong>Share My Journey:</strong> This feature allows a trusted contact to follow your location in real-time. Press the button to start a journey. The app will copy a unique link to your clipboard and prompt you to share it. Send this link to your contact via text or any messaging app. They can then view your live position on a map. Press the button again to stop sharing at any time.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-sound-scape" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Sound Scape</summary>
                        <div class="p-4">
                            <ul class="list-disc list-inside mt-2 space-y-2">
                                <li><strong>Sound Scape:</strong> Capture the moment with a voice memo. Use this to record your thoughts, feelings, or the sounds of your location. Press the button to start and stop recording. The audio file will be automatically saved to your device's download folder.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-trip-planner" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Trip Planner</summary>
                        <div class="p-4">
                            <p>Planning a multi-stop adventure? The <strong class="text-sky-600">Trip</strong> tab helps you build and analyze a detailed itinerary.</p>
                            <ol class="list-decimal list-inside mt-2 space-y-2">
                                <li><strong>Add Stops:</strong> Enter an attraction name (either by typing in the name or selecting a location on the map), your planned arrival time, and the duration of your stay in hours (e.g., "2" for two hours). Enter up to 10 stops. Time calulation only works for 1 day (midnight to midnight).</li>
                                <li><strong>Calculate Travel:</strong> Once you have at least two stops, click this button. The app will calculate the travel time between each stop and the total travel time for your entire trip.</li>
                                <li><strong>Analyze Itinerary:</strong> After calculating travel times, this button becomes active. Click it to get a detailed analysis of your schedule. The app will check for any timing conflicts (e.g., not enough time to travel between stops) and provide suggestions where you might have extra time available.</li>
                                <li><strong>Save/Export:</strong> Once you have an itinerary completed, you can save it for future reference or export it to share with others via another app (email/drive etc).</li>
                            </ol>
                        </div>
                    </details>

                    <details id="guide-activities" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Activities</summary>
                        <div class="p-4">
                            <p>If you're part of an organized tour or group activity, use the <strong class="text-sky-600">Activ</strong> tab.</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Join an Activity:</strong> Enter the unique code provided by your tour operator to add the activity to your itinerary.</li>
                                <li><strong>Activity Details:</strong> Tap on an activity to see its details, including information about your guide and any relevant documents provided by the tour company.</li>
                                <li><strong>Activity Chat & Photos:</strong> From the details screen, you can access a dedicated hub for that activity. Use the "Chat" tab to send text messages to other participants and the "Photos" tab to share and view images from the event.</li>
                                <li><strong>Explore Activities:</strong> Find your next activity from tour companies that have joined the Monoloci family. Explore by state/region by selecting the dropdown. Click on each listing for more information about what the company offers.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-journeys" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Journeys</summary>
                        <div class="p-4">
                            <p>To explore a theme or mystery journey, use the <strong class="text-sky-600">JRNY</strong> tab.</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Theme Journeys:</strong> Theme Journeys contain location nodes with unlock bonuses. The locations are visible and can be traveled in any order. Select the theme journey you would like to explore and then click on each node to discover the location/destination/venue. Unlock all nodes to access your reward.</li>
                                <li><strong>Mystery Journeys:</strong> Mystery Journeys contain multiple locations with a final destination. You must visit the locations in order. Clues are provided to help you find each location and the map will direct you with only the next 800m visible. We have driving and walking mystery journeys available.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-notes" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Notes</summary>
                        <div class="p-4">
                             <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Notes:</strong> Add notes for your journey such as Packing list, Interested locations and activities and a folder shortcut to access essential trip documents stored on your device.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-info-settings" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Info/Settings</summary>
                        <div class="p-4">
                            <p>The <strong class="text-sky-600">Info</strong> tab is your hub for settings and app information.</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>SOS Settings:</strong> This is the most important section to configure. Here you can set your emergency contact's phone number and customize the default SOS message that will be sent to them.</li>
                                <li><strong>App User Guide:</strong> You're reading it!</li>
                                <li><strong>Sponsors:</strong> View our partners.</li>
                                <li><strong>Install App:</strong> For the best experience, install Monoloci 'as an app' to your device's home screen. If the app is auto installable on your device, you will see a button on the Info screen, or, Manually install for different browsers: Safari Mobile (iOS): Share (square with arrow) > Scroll down and tap "Add to Home Screen" > Customize name and tap "Add" > App appears on home screen like a native app. | Firefox browser: Settings > Add app to Home Screen. | Chrome, Edge, Brave, Samsung; Settings > Install app.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-permissions" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Permissions</summary>
                        <div class="p-4">
                            <p class="text-slate-600 mb-3">For the app to work properly, you'll need to enable certain device permissions:</p>
                            <ul class="list-disc list-inside mt-2 space-y-1 text-slate-600">
                                <li><strong>GPS/Location Access:</strong> Required for Share My Journey, Theme Journeys, and Mystery Journeys. This allows the app to track your location and provide location-based features.</li>
                                <li><strong>Microphone Access:</strong> Required for SoundScape feature. This allows you to record audio notes during your activities.</li>
                                <li><strong>File/Document Access:</strong> Required to access documents folder from the Notes section. This allows you to open a selected folder on your device for storing travel documents.</li>
                            </ul>
                            <p class="text-sm text-slate-500 mt-3">Note: The app will prompt you for these permissions when you first use each feature. You can also manage permissions in your device's settings.</p>
                        </div>
                    </details>

                     <details id="guide-support" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">Support</summary>
                        <div class="p-4">
                             <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Contact:</strong> Need support or to share feedback about the app? Email us: <a href="mailto:<EMAIL>" class="text-blue-600 underline"><EMAIL></a></li>
                                <li><strong>Socials:</strong> Follow us on: <a href="https://www.facebook.com/monolociaustralia" target="_blank" class="text-blue-600 underline">Facebook</a> and <a href="https://www.youtube.com/@monoloci" target="_blank" class="text-blue-600 underline">Youtube</a>.</li>
                                <li><strong>Community:</strong> Join our <a href="https://monoloci.xyz/community" target="_blank" class="text-blue-600 underline">Monoloci Community</a>.</li>
                                <li><strong>Privacy:</strong> View our <a href="https://monoloci.xyz/privacy-policy" target="_blank" class="text-blue-600 underline">Privacy Policy</a>.</li>
                            </ul>
                        </div>
                    </details>

                    <details id="guide-name" class="mb-2 border rounded-lg overflow-hidden">
                        <summary class="p-4 font-bold text-slate-800 bg-slate-100 cursor-pointer">The Name</summary>
                        <div class="p-4">
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Mono (One):</strong> From Greek monos — “one”, “singular”, “unified”. Mirrors unity, but not isolation. </li>
                                <li><strong>Mono (Monolith):</strong> Monolith — “single stone”. The monolith is not just a stone. In mythos, monoliths are transmission beacons, often left by ancient intelligences to activate memory.</li>
                                <li><strong>Loci:</strong> Plural of locus, Latin for “place”, “focus point” or “node of attention”.</li>
                                <li><strong>Monoloci:</strong> “A unified field of many sacred points”. “One app, many gateways”. “The singular presence within multiple places”.</li>
                            </ul>
                        </div>
                    </details>
                </div>
                </div>
                <!-- Notes Screen -->
                <div id="notes-screen" class="hidden flex flex-col main-content">
                    <div class="p-3 border-b border-slate-200 bg-slate-50 text-center flex items-center">
                        <button id="notes-back-to-info" class="bg-slate-200 text-slate-700 py-1 px-3 rounded-md hover:bg-slate-300 transition-colors">&larr; Back</button>
                        <div class="flex-1"><h1 class="text-xl font-bold text-slate-800">My Notes</h1></div><div class="w-10"></div>
                    </div>
                    <div class="flex-1 p-6 overflow-y-auto space-y-6">
                        <!-- Packing List -->
                        <div>
                            <h2 class="text-lg font-semibold text-slate-700 mb-2">Packing List</h2>
                            <div id="packing-list" class="space-y-2"></div>
                            <div class="flex gap-2 mt-2">
                                <input type="text" id="packing-list-input" placeholder="New item..." class="flex-1 p-2 border border-slate-300 rounded-md">
                                <button id="add-packing-item-button" class="px-4 py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Add</button>
                            </div>
                        </div>
                        <!-- Interests -->
                        <div>
                            <h2 class="text-lg font-semibold text-slate-700 mb-2">Interests (Locations/Activities)</h2>
                            <textarea id="interests-textarea" rows="5" class="w-full p-2 border border-slate-300 rounded-md" placeholder="Jot down places you want to visit or things you want to do..."></textarea>
                        </div>
                        <!-- Documents -->
                        <div>
                            <h2 class="text-lg font-semibold text-slate-700 mb-2">Documents Folder</h2>
                            <p class="text-sm text-slate-600 mb-2">This will open a dialog to select your documents folder. Create a folder on your device and store all your important docs inside, then use this button to open the folder for quick access.</p>
                            <input type="file" id="document-folder-input" webkitdirectory directory multiple class="hidden">
                            <button id="select-document-folder-button" class="w-full py-3 bg-indigo-500 text-white rounded-lg font-semibold hover:bg-indigo-600">Select Folder</button>
                            <div id="document-list-notes" class="mt-4 space-y-2"></div>
                        </div>
                    </div>
                </div>
                <!-- Sponsors Screen -->
                <div id="sponsors-screen" class="hidden flex flex-col main-content">
                    <div class="p-3 border-b border-slate-200 bg-slate-50 text-center flex items-center">
                        <button id="sponsors-back-to-info" class="bg-slate-200 text-slate-700 py-1 px-3 rounded-md hover:bg-slate-300 transition-colors">&larr; Back</button>
                        <div class="flex-1"><h1 class="text-xl font-bold text-slate-800">Our Sponsors</h1></div><div class="w-10"></div>
                    </div>
                <div class="p-4 space-y-4 overflow-y-auto">
                    <!-- Sponsor 1 -->
                    <a href="https://matwinmedia.au" target="_blank" rel="noopener noreferrer" class="block p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4 hover:bg-slate-50">
                        <img src="https://firebasestorage.googleapis.com/v0/b/aura-app-backend.firebasestorage.app/o/core-app-images%2FMWM-logo.webp?alt=media&token=0051d6bb-49ea-4986-b4d1-bb3f36ccc55b" alt="MatWin Media Logo" class="w-20 h-20 rounded-md object-cover">
                        <div>
                            <p class="font-bold text-slate-800">MatWin Media</p>
                            <p class="text-sm text-slate-600">matwinmedia.au</p>
                        </div>
                    </a>
                    <!-- Sponsor 2 -->
                    <a href="https://energywellness.au" target="_blank" rel="noopener noreferrer" class="block p-4 bg-white border rounded-lg shadow-sm flex items-center gap-4 hover:bg-slate-50">
                        <img src="https://firebasestorage.googleapis.com/v0/b/aura-app-backend.firebasestorage.app/o/core-app-images%2FEW-Logo.png?alt=media&token=e9fd036d-3520-48f9-8037-dc777c489a9a" alt="Energy Wellness Logo" class="w-20 h-20 rounded-md object-cover">
                        <div>
                            <p class="font-bold text-slate-800">Energy Wellness Marketplace</p>
                            <p class="text-sm text-slate-600">energywellness.au</p>
                        </div>
                    </a>
                    <!-- GIF Container -->
                    <div class="flex-1 flex items-center justify-center p-4">
                        <a href="https://pulselifeforce.com" target="_blank" rel="noopener noreferrer" class="block w-full max-w-sm">
                            <img src="https://firebasestorage.googleapis.com/v0/b/aura-app-backend.firebasestorage.app/o/core-app-images%2Fpulse_.gif?alt=media&token=e8f86900-d2ff-4d55-a2a4-0ab11c71e239"
                                 alt="Pulse"
                                 class="w-full h-auto rounded-lg shadow-lg opacity-80 hover:opacity-100 transition-opacity">
                        </a>
                    </div>
                </div>
            </div>
            <!-- Activity Info Screen -->
            <div id="activity-info-screen" class="hidden flex flex-col main-content"></div>
            <!-- Chat Container -->
            <div id="chat-container" class="hidden flex flex-col h-full">
                <!-- Header -->
                <div class="p-3 border-b border-slate-200 bg-slate-50 flex items-center gap-2">
                    <button id="chat-back-to-activity-info" class="p-2 rounded-full hover:bg-slate-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-600"><path d="m15 18-6-6 6-6"/></svg></button>
                    <div class="flex-1 text-center">
                        <h1 id="chat-header-title" class="text-lg font-bold text-slate-800 truncate"></h1>
                        <p id="chat-header-subtitle" class="text-sm text-slate-500 truncate"></p>
                    </div>
                    <div class="w-10"></div> <!-- Spacer -->
                </div>
                <!-- Controls -->
                <div class="p-3 bg-slate-100 border-b border-slate-200 space-y-3">
                    <div class="flex justify-center items-center">
                        <div id="chat-mode-switcher" class="flex items-center p-1 bg-slate-300 rounded-full cursor-pointer">
                            <button id="mode-group" class="px-3 py-1 text-sm rounded-full bg-white text-slate-800 shadow font-semibold">Chat</button>
                            <button id="mode-photos" class="px-3 py-1 text-sm rounded-full text-slate-600 font-medium">Photos</button>
                        </div>
                    </div>
                </div>
                <!-- Messages -->
                <div id="chat-messages" class="flex-grow p-4 overflow-y-auto bg-slate-50"><div id="loading-spinner" class="flex justify-center items-center h-full"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"></div></div></div>
                <div id="photo-gallery" class="hidden flex-grow p-2 overflow-y-auto bg-slate-50">
                    <div id="gallery-grid" class="grid grid-cols-3 gap-2"></div>
                </div>
                <div id="upload-status" class="hidden px-4 pb-2 text-sm text-center text-sky-600"></div>
                <!-- Input Area -->
                <div id="message-input-area" class="flex-shrink-0 p-4 border-t border-slate-200 bg-white">
                    <form id="message-form" class="flex items-center gap-3">
                        <input type="file" id="file-input" class="hidden" accept="image/*">
                        <input type="text" id="message-input" placeholder="Type message..." class="flex-1 w-full px-4 py-2 bg-slate-100 border rounded-full focus:outline-none focus:ring-2 focus:ring-sky-500">
                        <button type="submit" class="p-3 bg-sky-500 text-white rounded-full hover:bg-sky-600"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 3 3 9-3 9 19-9Z"/><path d="M6 12h16"/></svg></button>
                    </form>
                </div>
                <div id="photo-upload-area" class="hidden flex-shrink-0 p-4 border-t border-slate-200 bg-white justify-center">
                    <button id="photo-upload-button" class="w-full max-w-xs py-3 bg-sky-500 text-white rounded-lg font-semibold hover:bg-sky-600 active:scale-95 flex items-center justify-center gap-2 shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>
                        <span>Upload Photo</span>
                    </button>
                </div>
            </div>

            <!-- Codex Screen -->
            <div id="codex-screen" class="hidden main-content">
                <div id="codex-root" class="h-full"></div>
            </div>
        </div>
        <!-- Main Navigation Tabs -->
        <div class="grid grid-cols-5 border-t main-nav">
            <button id="nav-home" class="flex flex-col items-center pt-2 pb-1 px-1 text-sky-600 bg-sky-100">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                <span class="text-xs font-medium">Home</span>
            </button>
            <button id="nav-solo" class="flex flex-col items-center pt-2 pb-1 px-1 text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"/><circle cx="12" cy="10" r="3"/></svg>
                <span class="text-xs font-medium">Trip</span>
            </button>
            <button id="nav-codex" class="flex flex-col items-center pt-2 pb-1 px-1 text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles"><path d="m12 3-1.9 5.8-5.8 1.9 5.8 1.9 1.9 5.8 1.9-5.8 5.8-1.9-5.8-1.9z"/></svg>
                <span class="text-xs font-medium">JRNY</span>
            </button>
            <button id="nav-group" class="flex flex-col items-center pt-2 pb-1 px-1 text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
                <span class="text-xs font-medium">Activ</span>
            </button>
            <button id="nav-info" class="flex flex-col items-center pt-2 pb-1 px-1 text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                <span class="text-xs font-medium">Info</span>
            </button>
        </div>
    </div>

    <!-- Modals -->
    <div id="settings-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full shadow-xl">
            <div class="flex justify-between items-center mb-4"><h2 class="text-xl font-bold text-slate-800">Emergency Settings</h2><button id="close-settings-button" class="p-2 rounded-full hover:bg-slate-200 text-2xl leading-none">&times;</button></div>
            <form id="settings-form">
                <div class="mb-4"><label for="emergency-contact" class="block text-sm font-medium text-slate-700 mb-1">Emergency Contact Number</label><input type="tel" id="emergency-contact" class="w-full p-2 border border-slate-300 rounded-md" placeholder="e.g., 0412345678"></div>
                <div class="mb-4"><label for="emergency-message" class="block text-sm font-medium text-slate-700 mb-1">Default SOS Message</label><textarea id="emergency-message" rows="3" class="w-full p-2 border border-slate-300 rounded-md" placeholder="I am in an emergency..."></textarea></div>
                <div class="mt-6 pt-4 border-t border-slate-200"><fieldset><legend class="text-sm font-medium text-slate-700 mb-2">Default SOS Action</legend><div class="space-y-2"><div class="flex items-center"><input type="radio" id="sos-action-show-options" name="sos-action" value="show-options" class="h-4 w-4 text-sky-600 border-gray-300 focus:ring-sky-500"><label for="sos-action-show-options" class="ml-3 block text-sm text-slate-600">Show options menu</label></div><div class="flex items-center"><input type="radio" id="sos-action-alert-contact" name="sos-action" value="alert-contact" class="h-4 w-4 text-sky-600 border-gray-300 focus:ring-sky-500"><label for="sos-action-alert-contact" class="ml-3 block text-sm text-slate-600">Instantly alert emergency contact</label></div></div></fieldset></div>
                <button type="submit" class="w-full mt-6 py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Save Settings</button>
            </form>
        </div>
    </div>
    <div id="sos-options-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center shadow-xl">
            <h2 class="text-2xl font-bold text-red-600">Emergency Options</h2><p class="text-slate-600 my-4">Choose an action. Location will be included.</p><div id="location-status" class="text-sm text-slate-500 mb-4 h-5">Getting your location...</div>
            <div class="space-y-3"><a id="call-emergency-link" href="tel:000" class="block w-full py-3 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700">Call Emergency (000)</a><button id="alert-contact-button" class="w-full py-3 bg-amber-500 text-white font-semibold rounded-lg hover:bg-amber-600">Alert Emergency Contact</button></div>
            <button id="cancel-sos" class="mt-6 text-sm text-slate-600 hover:underline">Cancel</button>
        </div>
    </div>
    <div id="action-confirm-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center shadow-xl">
            <h2 id="action-confirm-title" class="text-xl font-bold text-amber-500"></h2>
            <p id="action-confirm-text" class="text-slate-600 my-4"></p>
            <button id="close-action-confirm-modal" class="mt-4 w-full py-2 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300">OK</button>
        </div>
    </div>
    <div id="journey-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center shadow-xl">
            <h2 class="text-xl font-bold text-sky-600">Journey Sharing is Active</h2>
            <p id="journey-status-text" class="text-slate-600 my-4">Your location is being shared. Send the link below to your contact.</p>
            <div class="bg-slate-100 p-2 rounded-md break-all mb-4"><code id="journey-link" class="text-sm text-slate-700"></code></div>
            <button id="copy-journey-link" class="w-full py-2 bg-slate-200 text-slate-800 font-semibold rounded-lg hover:bg-slate-300 mb-4" disabled>Copy Link</button>
            <button id="stop-journey-button" class="w-full py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700">Stop Sharing</button>
        </div>
    </div>
    <div id="name-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full shadow-xl">
            <h2 class="text-xl font-bold text-slate-800">Enter Your Name for Chat</h2>
            <p class="text-slate-600 my-4">Please enter your name to be displayed in the chat.</p>
            <input type="text" id="name-input" class="w-full p-2 border border-slate-300 rounded-md" placeholder="Your Name for Chat">
            <button id="save-name-button" class="w-full mt-4 py-2 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Join Chat</button>
        </div>
    </div>

    <!-- PWA Install Modal -->
<!-- Directory Item Details Modal -->
    <div id="directory-item-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg w-full max-w-3xl shadow-xl relative">
            <div id="directory-item-content" class="max-h-[80vh] overflow-y-auto mb-4 text-left text-lg">
                <!-- Content will be injected here by JavaScript -->
            </div>
            <button id="close-directory-modal" class="w-full mt-3 bg-slate-600 text-white font-medium py-2 px-6 rounded-lg hover:bg-slate-700 transition-colors duration-300">Close</button>
        </div>
    </div>
    <div id="pwa-install-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center shadow-xl">
            <h2 class="text-xl font-bold text-slate-800">Install App</h2>
            <p class="text-slate-600 my-4">For the best experience, install the Monoloci app on your device. It's fast and works offline.</p>
            <div class="space-y-3">
                <button id="pwa-install-confirm-button" class="w-full py-3 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Install</button>
            </div>
            <button id="pwa-install-cancel-button" class="mt-6 text-sm text-slate-600 hover:underline">Not now</button>
        </div>
    </div>

<script type="module" src="./app.js"></script>
<!-- codex.js will be loaded dynamically by app.js -->
<script type="module" src="userGuide.js"></script>

<!-- PWA Update Modal -->
<div id="pwa-update-modal" class="hidden fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
    <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center shadow-xl">
        <h2 class="text-xl font-bold text-slate-800">Update Available</h2>
        <p class="text-slate-600 my-4">A new version of the app is available. Refresh to get the latest features.</p>
        <div class="space-y-3">
            <button id="pwa-update-confirm-button" class="w-full py-3 bg-sky-500 text-white font-semibold rounded-lg hover:bg-sky-600">Refresh</button>
        </div>
        <button id="pwa-update-cancel-button" class="mt-6 text-sm text-slate-600 hover:underline">Not now</button>
    </div>
</div>

<!-- Preloaded background image -->
<img src="./assets/unity.webp" alt="" style="display:none;">
    <!-- Conditional AdSense Loading -->
    <script type="module" src="./adsense-loader.js"></script>

</body>
</html>
