// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    ext {
        agp_version = '8.2.2'
        agp_version1 = '8.10.0'
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version1"
        classpath 'com.google.gms:google-services:4.4.3'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply from: "variables.gradle"

allprojects {
    repositories {
        google()
        mavenCentral()
    }
    
    // Force Java 21 for all modules
    tasks.withType(JavaCompile) {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    // Configure toolchain for all projects
    if (project.hasProperty('java')) {
        java {
            toolchain {
                languageVersion = JavaLanguageVersion.of(21)
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
