#!/usr/bin/env node

/**
 * Automated Security Fixes Script
 * Applies immediate security fixes to the Monoloci app
 */

const fs = require('fs');
const { execSync } = require('child_process');

class SecurityFixer {
    constructor() {
        this.fixes = [];
        this.errors = [];
    }

    // Fix 1: Update vulnerable dependencies
    async fixDependencyVulnerabilities() {
        console.log('🔧 Fixing dependency vulnerabilities...');
        
        try {
            // Run npm audit fix
            execSync('npm audit fix', { stdio: 'inherit' });
            this.fixes.push('✅ Dependency vulnerabilities fixed with npm audit fix');
            
            // Update major versions
            execSync('npm update firebase@latest', { stdio: 'inherit' });
            this.fixes.push('✅ Firebase updated to latest version');
            
            execSync('npm update react@latest react-dom@latest', { stdio: 'inherit' });
            this.fixes.push('✅ React updated to latest version');
            
        } catch (error) {
            this.errors.push(`❌ Failed to update dependencies: ${error.message}`);
        }
    }

    // Fix 2: Remove hardcoded API keys
    fixHardcodedAPIKeys() {
        console.log('🔧 Securing hardcoded API keys...');
        
        // Check if app-changed-auth.js exists and contains hardcoded keys
        if (fs.existsSync('app-changed-auth.js')) {
            const content = fs.readFileSync('app-changed-auth.js', 'utf8');
            
            if (content.includes('AIzaSy')) {
                // Create backup
                fs.writeFileSync('app-changed-auth.js.backup', content);
                
                // Replace hardcoded config with environment-based config
                const secureContent = content.replace(
                    /const firebaseConfig = \{[\s\S]*?\};/,
                    `const firebaseConfig = window.FIREBASE_CONFIG || {
  // Fallback configuration - should be replaced by build process
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID
};`
                );
                
                fs.writeFileSync('app-changed-auth.js', secureContent);
                this.fixes.push('✅ Hardcoded API keys secured in app-changed-auth.js');
            }
        }
    }

    // Fix 3: Enhance CSP configuration
    enhanceCSP() {
        console.log('🔧 Enhancing Content Security Policy...');
        
        if (fs.existsSync('index.html')) {
            let content = fs.readFileSync('index.html', 'utf8');
            
            // Check if CSP meta tag exists
            if (!content.includes('Content-Security-Policy')) {
                // Add CSP meta tag after charset
                const cspTag = `    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://apis.google.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.googleapis.com https://*.firebaseio.com wss://*.firebaseio.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self';">`;
                
                content = content.replace(
                    /<meta charset="utf-8">/,
                    `<meta charset="utf-8">\n${cspTag}`
                );
                
                fs.writeFileSync('index.html', content);
                this.fixes.push('✅ Content Security Policy added to index.html');
            } else {
                this.fixes.push('ℹ️ Content Security Policy already exists');
            }
        }
    }

    // Fix 4: Add security headers configuration
    createSecurityHeadersConfig() {
        console.log('🔧 Creating security headers configuration...');
        
        // Create .htaccess for Apache
        const htaccessContent = `# Security Headers
<IfModule mod_headers.c>
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(self), microphone=(self), camera=(self)"
</IfModule>

# Force HTTPS
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>`;
        
        fs.writeFileSync('.htaccess', htaccessContent);
        this.fixes.push('✅ Apache security headers configuration created (.htaccess)');
        
        // Create nginx configuration
        const nginxContent = `# Security Headers for Nginx
# Add these to your server block

add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "geolocation=(self), microphone=(self), camera=(self)" always;

# Force HTTPS
if ($scheme != "https") {
    return 301 https://$host$request_uri;
}`;
        
        fs.writeFileSync('nginx-security-headers.conf', nginxContent);
        this.fixes.push('✅ Nginx security headers configuration created');
    }

    // Fix 5: Enhance Firebase security rules
    enhanceFirebaseRules() {
        console.log('🔧 Enhancing Firebase security rules...');
        
        if (fs.existsSync('firestore.rules')) {
            const content = fs.readFileSync('firestore.rules', 'utf8');
            
            // Check if enhanced validation functions exist
            if (!content.includes('isValidUserData')) {
                const enhancedRules = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Enhanced user data protection
    match /users/{userId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && (request.method == 'get' || isValidUserData(request.resource.data));
      
      // Rate limiting for sensitive operations
      match /rateLimiting/{document} {
        allow write: if request.auth != null 
          && request.auth.uid == userId
          && isWithinRateLimit();
      }
      
      match /redeemed_coupons/{couponId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
      
      match /journey_progress/{journeyId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Enhanced journey security
    match /journeys/{journeyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && (resource == null || resource.data.createdBy == request.auth.uid)
        && isValidJourneyData(request.resource.data);
    }
    
    // Public read access for artifacts and codexes
    match /artifacts/aura-global-app/public/{document=**} {
      allow read: if true;
    }
    
    match /codexes/{codexId}/{document=**} {
      allow read: if true;
    }
    
    // Validation functions
    function isValidUserData(data) {
      return data != null 
        && (!data.keys().hasAny(['name']) || (data.name is string && data.name.size() <= 100))
        && (!data.keys().hasAny(['email']) || (data.email is string && data.email.matches('.*@.*\\\\..*')));
    }
    
    function isValidJourneyData(data) {
      return data != null
        && data.keys().hasAll(['createdBy'])
        && (!data.keys().hasAny(['title']) || (data.title is string && data.title.size() <= 200));
    }
    
    function isWithinRateLimit() {
      return resource == null || request.time > resource.data.timestamp + duration.value(1, 'm');
    }
  }
}`;
                
                // Backup original rules
                fs.writeFileSync('firestore.rules.backup', content);
                fs.writeFileSync('firestore.rules', enhancedRules);
                this.fixes.push('✅ Firebase security rules enhanced with validation functions');
            } else {
                this.fixes.push('ℹ️ Firebase security rules already enhanced');
            }
        }
    }

    // Fix 6: Add package.json security scripts
    addSecurityScripts() {
        console.log('🔧 Adding security scripts to package.json...');
        
        if (fs.existsSync('package.json')) {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            // Add security scripts
            packageJson.scripts = packageJson.scripts || {};
            packageJson.scripts['security:audit'] = 'npm audit';
            packageJson.scripts['security:fix'] = 'npm audit fix';
            packageJson.scripts['security:test'] = 'node security-test-suite.js';
            packageJson.scripts['security:check'] = 'npm run security:audit && npm run security:test';
            
            fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
            this.fixes.push('✅ Security scripts added to package.json');
        }
    }

    // Fix 7: Create environment template
    createEnvironmentTemplate() {
        console.log('🔧 Creating environment template...');
        
        const envTemplate = `# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Google Maps Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Environment
NODE_ENV=production
APP_ENVIRONMENT=production

# Security Settings
ENABLE_SECURITY_LOGGING=true
SECURITY_LOG_LEVEL=info`;
        
        if (!fs.existsSync('.env.example')) {
            fs.writeFileSync('.env.example', envTemplate);
            this.fixes.push('✅ Environment template created (.env.example)');
        }
        
        // Create .gitignore if it doesn't exist
        if (!fs.existsSync('.gitignore')) {
            const gitignoreContent = `# Environment variables
.env
.env.local
.env.production

# Dependencies
node_modules/

# Build outputs
dist/
build/

# Logs
*.log
npm-debug.log*

# Security
security-test-results.json
*.backup

# OS
.DS_Store
Thumbs.db`;
            
            fs.writeFileSync('.gitignore', gitignoreContent);
            this.fixes.push('✅ .gitignore created with security exclusions');
        }
    }

    // Run all fixes
    async runAllFixes() {
        console.log('🚀 Starting automated security fixes...\n');
        
        await this.fixDependencyVulnerabilities();
        this.fixHardcodedAPIKeys();
        this.enhanceCSP();
        this.createSecurityHeadersConfig();
        this.enhanceFirebaseRules();
        this.addSecurityScripts();
        this.createEnvironmentTemplate();
        
        this.generateReport();
    }

    // Generate report
    generateReport() {
        console.log('\n📊 SECURITY FIXES REPORT\n');
        console.log('=' .repeat(50));
        
        console.log(`✅ Fixes Applied: ${this.fixes.length}`);
        console.log(`❌ Errors: ${this.errors.length}\n`);
        
        if (this.fixes.length > 0) {
            console.log('APPLIED FIXES:');
            console.log('-'.repeat(30));
            this.fixes.forEach(fix => console.log(fix));
        }
        
        if (this.errors.length > 0) {
            console.log('\nERRORS:');
            console.log('-'.repeat(30));
            this.errors.forEach(error => console.log(error));
        }
        
        console.log('\n🔒 NEXT STEPS:');
        console.log('1. Review and test all applied fixes');
        console.log('2. Configure your hosting platform with security headers');
        console.log('3. Set up environment variables for production');
        console.log('4. Deploy Firebase security rules');
        console.log('5. Run security tests: npm run security:check');
        console.log('6. Monitor security dashboard after deployment');
    }
}

// Run the security fixer
if (require.main === module) {
    const fixer = new SecurityFixer();
    fixer.runAllFixes();
}

module.exports = SecurityFixer;
