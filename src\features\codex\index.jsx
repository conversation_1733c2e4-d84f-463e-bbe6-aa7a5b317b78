import React from 'react';
import <PERSON>actDOM from 'react-dom/client';
import Codex from './Codex';

let root = null; // Keep track of the root instance

// Expose a render function to be called by the main app
export function render(container) {
    if (container) {
        if (!root) { // Only create the root if it doesn't exist
            root = ReactDOM.createRoot(container);
        }
        // Render the component into the root
        root.render(
            <React.StrictMode>
                <Codex />
            </React.StrictMode>
        );
    } else {
        console.error('Failed to find the root element for the Codex feature.');
    }
}

// Auto-render if the script is loaded directly in a browser for standalone testing
const container = document.getElementById('codex-root');
if (container) {
    render(container);
}