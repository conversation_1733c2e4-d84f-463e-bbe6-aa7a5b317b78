@echo off
echo.
echo ========================================
echo    Monoloci App - Local Development Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo 💡 Please install Python from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check if required files exist
if not exist "index.html" (
    echo ❌ index.html not found
    set missing=1
)
if not exist "app.js" (
    echo ❌ app.js not found
    set missing=1
)
if not exist "style.css" (
    echo ❌ style.css not found
    set missing=1
)

if defined missing (
    echo.
    echo ⚠️  Some files are missing. The app may not work correctly.
    echo.
)

echo 🚀 Starting server on http://localhost:8000
echo.
echo 💡 The diagnostic page will open automatically
echo    If the main app doesn't work, try the fallback version
echo.
echo 🛑 Press Ctrl+C to stop the server
echo.

REM Start the Python server
python start-server.py

echo.
echo 👋 Server stopped. Press any key to exit.
pause >nul
