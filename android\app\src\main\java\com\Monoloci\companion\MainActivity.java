package com.Monoloci.companion;

import com.getcapacitor.BridgeActivity;
import com.google.firebase.FirebaseApp;
import android.os.Bundle;
import com.tchvu3.capacitorvoicerecorder.VoiceRecorder;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize Firebase
        if (FirebaseApp.getApps(this).isEmpty()) {
            FirebaseApp.initializeApp(this);
        }
        
        registerPlugin(VoiceRecorder.class);
    }
}
