// This script now retrieves the globally shared Firebase instance
// initialized by the main app.js file. This prevents conflicts
// from re-initializing the Firebase app.

// Create a promise that resolves when Firebase is ready
let firebasePromise = null;

function waitForFirebase() {
  if (firebasePromise) {
    return firebasePromise;
  }

  firebasePromise = new Promise((resolve, reject) => {
    // If firebase is already ready (e.g., on a soft navigation), resolve immediately.
    if (window.auraFirebase && typeof window.auraFirebase.onAuthStateChanged === 'function') {
      console.log("Codex: Firebase was already available.");
      resolve(window.auraFirebase);
      return;
    }

    // Otherwise, wait for the custom event from app.js
    console.log("Codex: Waiting for firebase-ready event...");
    
    const onReady = () => {
        console.log("Codex: Received firebase-ready event.");
        if (window.auraFirebase && typeof window.auraFirebase.onAuthStateChanged === 'function') {
            resolve(window.auraFirebase);
        } else {
            console.error("Codex: firebase-ready event was dispatched, but window.auraFirebase is incomplete.");
            reject(new Error("Firebase initialization failed after ready signal."));
        }
    };

    window.addEventListener('firebase-ready', onReady, { once: true });

    // Timeout to prevent waiting forever if the event never fires
    // Increased timeout for Android WebView which can be slower
    const timeoutMs = navigator.userAgent.includes('wv') ? 20000 : 10000;
    setTimeout(() => {
        console.error("Firebase timeout debug info:", {
            hasWindow: typeof window !== 'undefined',
            hasAuraFirebase: !!window.auraFirebase,
            auraFirebaseKeys: window.auraFirebase ? Object.keys(window.auraFirebase) : [],
            userAgent: navigator.userAgent.substring(0, 100)
        });
        reject(new Error(`Timeout: Waited ${timeoutMs/1000} seconds for firebase-ready event, but it was never dispatched.`));
    }, timeoutMs);
  });

  return firebasePromise;
}

// Export the promise-based Firebase access
export const getFirebase = waitForFirebase;

// Legacy exports for backward compatibility (but these should not be used directly)
export let db = null;
export let auth = null;

// Initialize legacy exports when Firebase is ready
waitForFirebase().then((firebaseServices) => {
  db = firebaseServices.db;
  auth = firebaseServices.auth;
}).catch(error => {
  console.error("Failed to initialize Firebase in Codex feature:", error);
});