#!/usr/bin/env node

/**
 * Comprehensive Security Test Suite for Monoloci App
 * Performs automated security testing and vulnerability assessment
 */

const fs = require('fs');
const path = require('path');

class SecurityTestSuite {
    constructor() {
        this.results = {
            critical: [],
            high: [],
            medium: [],
            low: [],
            passed: []
        };
    }

    // Test for hardcoded secrets and API keys
    testHardcodedSecrets() {
        console.log('🔍 Testing for hardcoded secrets...');
        
        const files = ['app.js', 'app-changed-auth.js', 'config.js'];
        const secretPatterns = [
            /AIzaSy[A-Za-z0-9_-]{33}/g, // Google API keys
            /sk_live_[A-Za-z0-9]{24}/g, // Stripe live keys
            /sk_test_[A-Za-z0-9]{24}/g, // Stripe test keys
            /access_token['":\s]*['"]\w+['"]/gi,
            /secret['":\s]*['"]\w+['"]/gi,
            /password['":\s]*['"]\w+['"]/gi
        ];

        files.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                secretPatterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        this.results.critical.push({
                            test: 'Hardcoded Secrets',
                            file: file,
                            issue: `Found potential hardcoded secret: ${matches[0].substring(0, 20)}...`,
                            line: this.getLineNumber(content, matches[0])
                        });
                    }
                });
            }
        });
    }

    // Test for XSS vulnerabilities
    testXSSVulnerabilities() {
        console.log('🔍 Testing for XSS vulnerabilities...');
        
        const files = ['app.js', 'dist/app.js'];
        const xssPatterns = [
            /\.innerHTML\s*=\s*[^`'"]*\$\{[^}]*\}/g, // Template literal injection
            /\.innerHTML\s*=\s*.*\+/g, // String concatenation
            /document\.write\s*\(/g, // document.write usage
            /eval\s*\(/g, // eval usage
            /setTimeout\s*\(\s*['"][^'"]*\+/g, // setTimeout with string concatenation
        ];

        files.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                xssPatterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        matches.forEach(match => {
                            // Check if XSS protection is used
                            const lineNum = this.getLineNumber(content, match);
                            const line = content.split('\n')[lineNum - 1];
                            
                            if (!line.includes('escapeHTML') && !line.includes('XSSProtection')) {
                                this.results.high.push({
                                    test: 'XSS Vulnerability',
                                    file: file,
                                    issue: `Potential XSS: ${match}`,
                                    line: lineNum
                                });
                            } else {
                                this.results.passed.push({
                                    test: 'XSS Protection',
                                    file: file,
                                    message: 'XSS protection detected'
                                });
                            }
                        });
                    }
                });
            }
        });
    }

    // Test Firebase security configuration
    testFirebaseConfiguration() {
        console.log('🔍 Testing Firebase configuration...');
        
        if (fs.existsSync('firestore.rules')) {
            const rules = fs.readFileSync('firestore.rules', 'utf8');
            
            // Check for overly permissive rules
            if (rules.includes('allow read, write: if true')) {
                this.results.high.push({
                    test: 'Firebase Security',
                    file: 'firestore.rules',
                    issue: 'Overly permissive Firebase rules detected',
                    line: this.getLineNumber(rules, 'allow read, write: if true')
                });
            }
            
            // Check for authentication requirements
            if (rules.includes('request.auth != null')) {
                this.results.passed.push({
                    test: 'Firebase Authentication',
                    file: 'firestore.rules',
                    message: 'Authentication requirements found'
                });
            }
        }
    }

    // Test for insecure dependencies
    testDependencyVulnerabilities() {
        console.log('🔍 Testing dependency vulnerabilities...');
        
        if (fs.existsSync('package.json')) {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
            
            // Check for known vulnerable packages (updated after fixes)
            const vulnerablePackages = {
                // All major dependencies have been updated to latest versions
            };
            
            Object.keys(dependencies).forEach(pkg => {
                if (vulnerablePackages[pkg]) {
                    this.results.medium.push({
                        test: 'Dependency Vulnerability',
                        file: 'package.json',
                        issue: `${pkg}: ${vulnerablePackages[pkg].issue}`,
                        current: dependencies[pkg],
                        recommended: 'Update to latest version'
                    });
                }
            });
        }
    }

    // Test for insecure HTTP usage
    testHTTPSUsage() {
        console.log('🔍 Testing HTTPS usage...');
        
        const files = ['app.js', 'index.html'];
        files.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                const httpMatches = content.match(/http:\/\/[^"'\s]+/g);
                
                if (httpMatches) {
                    httpMatches.forEach(match => {
                        if (!match.includes('localhost') &&
                            !match.includes('127.0.0.1') &&
                            !match.includes('http://www.w3.org/2000/svg')) {
                            this.results.medium.push({
                                test: 'Insecure HTTP',
                                file: file,
                                issue: `Insecure HTTP URL found: ${match}`,
                                line: this.getLineNumber(content, match)
                            });
                        }
                    });
                }
            }
        });
    }

    // Test for proper error handling
    testErrorHandling() {
        console.log('🔍 Testing error handling...');
        
        if (fs.existsSync('app.js')) {
            const content = fs.readFileSync('app.js', 'utf8');
            
            // Check for console.error with sensitive data
            const errorMatches = content.match(/console\.(error|log)\([^)]*error[^)]*\)/g);
            if (errorMatches) {
                this.results.low.push({
                    test: 'Error Information Disclosure',
                    file: 'app.js',
                    issue: 'Potential sensitive information in error logs',
                    count: errorMatches.length
                });
            }
            
            // Check for try-catch blocks
            const tryCatchCount = (content.match(/try\s*\{/g) || []).length;
            const catchCount = (content.match(/catch\s*\(/g) || []).length;
            
            if (tryCatchCount > 0 && tryCatchCount === catchCount) {
                this.results.passed.push({
                    test: 'Error Handling',
                    file: 'app.js',
                    message: `Found ${tryCatchCount} properly handled try-catch blocks`
                });
            }
        }
    }

    // Helper function to get line number
    getLineNumber(content, searchString) {
        const lines = content.substring(0, content.indexOf(searchString)).split('\n');
        return lines.length;
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting Security Test Suite...\n');
        
        this.testHardcodedSecrets();
        this.testXSSVulnerabilities();
        this.testFirebaseConfiguration();
        this.testDependencyVulnerabilities();
        this.testHTTPSUsage();
        this.testErrorHandling();
        
        this.generateReport();
    }

    // Generate comprehensive report
    generateReport() {
        console.log('\n📊 SECURITY TEST RESULTS\n');
        console.log('=' .repeat(50));
        
        const totalIssues = this.results.critical.length + this.results.high.length + 
                           this.results.medium.length + this.results.low.length;
        
        console.log(`🚨 Critical Issues: ${this.results.critical.length}`);
        console.log(`⚠️  High Priority: ${this.results.high.length}`);
        console.log(`📋 Medium Priority: ${this.results.medium.length}`);
        console.log(`ℹ️  Low Priority: ${this.results.low.length}`);
        console.log(`✅ Tests Passed: ${this.results.passed.length}`);
        console.log(`📊 Total Issues: ${totalIssues}\n`);
        
        // Detailed results
        ['critical', 'high', 'medium', 'low'].forEach(severity => {
            if (this.results[severity].length > 0) {
                console.log(`\n${severity.toUpperCase()} ISSUES:`);
                console.log('-'.repeat(30));
                this.results[severity].forEach((issue, index) => {
                    console.log(`${index + 1}. ${issue.test} in ${issue.file}`);
                    console.log(`   Issue: ${issue.issue}`);
                    if (issue.line) console.log(`   Line: ${issue.line}`);
                    if (issue.recommended) console.log(`   Fix: ${issue.recommended}`);
                    console.log('');
                });
            }
        });
        
        // Save results to file
        fs.writeFileSync('security-test-results.json', JSON.stringify(this.results, null, 2));
        console.log('📄 Detailed results saved to security-test-results.json');
    }
}

// Run the security test suite
if (require.main === module) {
    const testSuite = new SecurityTestSuite();
    testSuite.runAllTests();
}

module.exports = SecurityTestSuite;
