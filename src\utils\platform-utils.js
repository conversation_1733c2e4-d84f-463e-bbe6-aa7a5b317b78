/**
 * Platform Utilities for PWA/Native Compatibility
 * Provides unified APIs that work on both web and native platforms
 */

// Platform detection
export const isNativePlatform = () => {
    return window.Capacitor && window.Capacitor.isNativePlatform();
};

export const isWeb = () => !isNativePlatform();

// File picker with fallback
export const pickFile = async (options = {}) => {
    if (isNativePlatform()) {
        // Use Capacitor file picker for native
        const { FilePicker } = await import('@capawesome/capacitor-file-picker');
        try {
            const result = await FilePicker.pickFiles({
                types: options.accept ? [options.accept] : undefined,
                multiple: options.multiple || false,
                readData: true
            });
            return result.files;
        } catch (error) {
            console.error('Native file picker error:', error);
            throw error;
        }
    } else {
        // Use web file input for PWA
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            if (options.accept) input.accept = options.accept;
            if (options.multiple) input.multiple = true;
            if (options.directory) input.webkitdirectory = true;
            
            input.onchange = (event) => {
                const files = Array.from(event.target.files);
                resolve(files.map(file => ({
                    name: file.name,
                    size: file.size,
                    mimeType: file.type,
                    data: file, // For web, we keep the File object
                    blob: file
                })));
            };
            
            input.onerror = reject;
            input.click();
        });
    }
};

// Geolocation with unified API
export const getCurrentPosition = async (options = {}) => {
    if (isNativePlatform()) {
        const { Geolocation } = await import('@capacitor/geolocation');
        try {
            const position = await Geolocation.getCurrentPosition(options);
            return {
                coords: {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    altitude: position.coords.altitude,
                    altitudeAccuracy: position.coords.altitudeAccuracy,
                    heading: position.coords.heading,
                    speed: position.coords.speed
                },
                timestamp: position.timestamp
            };
        } catch (error) {
            console.error('Native geolocation error:', error);
            throw error;
        }
    } else {
        // Use web geolocation API
        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, options);
        });
    }
};

// Watch position with unified API
export const watchPosition = async (callback, errorCallback, options = {}) => {
    if (isNativePlatform()) {
        const { Geolocation } = await import('@capacitor/geolocation');
        try {
            const watchId = await Geolocation.watchPosition(options, (position) => {
                callback({
                    coords: {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        altitude: position.coords.altitude,
                        altitudeAccuracy: position.coords.altitudeAccuracy,
                        heading: position.coords.heading,
                        speed: position.coords.speed
                    },
                    timestamp: position.timestamp
                });
            });
            return watchId;
        } catch (error) {
            console.error('Native watch position error:', error);
            if (errorCallback) errorCallback(error);
            throw error;
        }
    } else {
        // Use web geolocation API
        return navigator.geolocation.watchPosition(callback, errorCallback, options);
    }
};

// Clear watch position
export const clearWatch = async (watchId) => {
    if (isNativePlatform()) {
        const { Geolocation } = await import('@capacitor/geolocation');
        await Geolocation.clearWatch({ id: watchId });
    } else {
        navigator.geolocation.clearWatch(watchId);
    }
};

// Camera with fallback
export const takePhoto = async (options = {}) => {
    if (isNativePlatform()) {
        const { Camera, CameraResultType, CameraSource } = await import('@capacitor/camera');
        try {
            const photo = await Camera.getPhoto({
                quality: options.quality || 90,
                allowEditing: options.allowEditing || false,
                resultType: CameraResultType.DataUrl,
                source: options.source || CameraSource.Prompt,
            });
            return {
                dataUrl: photo.dataUrl,
                format: photo.format,
                saved: photo.saved
            };
        } catch (error) {
            console.error('Native camera error:', error);
            throw error;
        }
    } else {
        // Use web file input for PWA
        return pickFile({ accept: 'image/*' });
    }
};

// Clipboard with fallback
export const writeToClipboard = async (text) => {
    if (isNativePlatform()) {
        const { Clipboard } = await import('@capacitor/clipboard');
        await Clipboard.write({ string: text });
    } else {
        // Use web clipboard API with fallback
        if (navigator.clipboard && navigator.clipboard.writeText) {
            await navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }
};

// Share API with fallback
export const share = async (shareData) => {
    if (isNativePlatform()) {
        const { Share } = await import('@capacitor/share');
        if (shareData.files && shareData.files.length > 0) {
            await Share.share({
                title: shareData.title,
                text: shareData.text,
                files: shareData.files,
            });
        } else {
            await Share.share({
                title: shareData.title,
                text: shareData.text,
                url: shareData.url,
                dialogTitle: shareData.title,
            });
        }
    } else {
        // Use web share API with fallback
        if (navigator.share && (!shareData.files || shareData.files.length === 0)) {
            await navigator.share(shareData);
        } else if (shareData.files && shareData.files.length > 0) {
            // Web Share API Level 2 for files
            if (navigator.canShare && navigator.canShare({ files: shareData.files })) {
                await navigator.share({
                    files: shareData.files,
                    title: shareData.title,
                    text: shareData.text,
                });
            } else {
                // Fallback for browsers that don't support file sharing
                alert("Your browser doesn't support sharing files.");
            }
        } else {
            // Fallback: copy to clipboard and show message
            await writeToClipboard(shareData.text || shareData.url);
            alert('Link copied to clipboard!');
        }
    }
};

// Device info
export const getDeviceInfo = async () => {
    if (isNativePlatform()) {
        const { Device } = await import('@capacitor/device');
        return await Device.getInfo();
    } else {
        return {
            platform: 'web',
            operatingSystem: navigator.platform,
            osVersion: navigator.userAgent,
            manufacturer: 'unknown',
            model: 'web',
            isVirtual: false,
            webViewVersion: navigator.userAgent
        };
    }
};

// Permissions helper
export const checkPermissions = async (permission) => {
    if (isNativePlatform()) {
        try {
            switch (permission) {
                case 'geolocation':
                    const { Geolocation } = await import('@capacitor/geolocation');
                    return await Geolocation.checkPermissions();
                case 'camera':
                    const { Camera } = await import('@capacitor/camera');
                    return await Camera.checkPermissions();
                default:
                    return { state: 'granted' };
            }
        } catch (error) {
            console.error('Permission check error:', error);
            return { state: 'denied' };
        }
    } else {
        // For web, we assume permissions are handled by browser
        return { state: 'granted' };
    }
};

// Request permissions
export const requestPermissions = async (permission) => {
    if (isNativePlatform()) {
        try {
            switch (permission) {
                case 'geolocation':
                    const { Geolocation } = await import('@capacitor/geolocation');
                    return await Geolocation.requestPermissions();
                case 'camera':
                    const { Camera } = await import('@capacitor/camera');
                    return await Camera.requestPermissions();
                default:
                    return { state: 'granted' };
            }
        } catch (error) {
            console.error('Permission request error:', error);
            return { state: 'denied' };
        }
    } else {
        // For web, permissions are requested when APIs are used
        return { state: 'granted' };
    }
};

// Firebase initialization helper
export const initializeFirebaseForPlatform = async (webConfig) => {
    if (isNativePlatform()) {
        // For native platforms, Firebase is initialized via google-services.json
        // We still need to initialize the web SDK for Firestore/Auth
        const { initializeApp } = await import('firebase/app');
        return initializeApp(webConfig);
    } else {
        // For web, use the standard web configuration
        const { initializeApp } = await import('firebase/app');
        return initializeApp(webConfig);
    }
};
