import { initializeApp } from "firebase/app";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAnalytics } from "firebase/analytics";

// Secure Firebase Configuration - loaded from config.js into the global window scope
const firebaseConfig = window.FIREBASE_CONFIG;

// Validate Firebase configuration
if (!firebaseConfig || !firebaseConfig.apiKey || firebaseConfig.apiKey.includes('your-')) {
  console.error('Firebase configuration not found or is invalid. Please ensure config.js is loaded properly and .env is configured.');
  throw new Error('Firebase configuration missing or invalid.');
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize and export Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Conditionally initialize Analytics to avoid errors on localhost
let analytics;
if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    try {
        analytics = getAnalytics(app);
    } catch (error) {
        console.warn("Firebase Analytics could not be initialized:", error);
    }
}

export { analytics };

// Expose necessary Firebase services globally for the Codex feature
// This maintains compatibility with the existing Codex implementation.
import { collection, getDocs, doc, getDoc, updateDoc, arrayUnion, setDoc, serverTimestamp } from "firebase/firestore";

window.auraFirebase = {
    db,
    auth,
    collection,
    getDocs,
    doc,
    getDoc,
    updateDoc,
    arrayUnion,
    setDoc,
    serverTimestamp,
    onAuthStateChanged
};

// Dispatch a custom event to signal that Firebase is fully initialized
// This is crucial for other scripts that depend on it, like the Codex feature.
window.dispatchEvent(new CustomEvent('firebase-ready'));
console.log("Dispatched firebase-ready event from firebase-init.js.");