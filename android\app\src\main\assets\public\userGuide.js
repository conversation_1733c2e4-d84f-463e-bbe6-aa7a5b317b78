(() => {
  // src/features/codex/userGuide.js
  document.addEventListener("DOMContentLoaded", () => {
    const setupGuideLink = (linkId, guideSectionId) => {
      const link = document.getElementById(linkId);
      if (link) {
        link.addEventListener("click", (e) => {
          e.preventDefault();
          document.getElementById("nav-info").click();
          document.getElementById("info-user-guide-button").click();
          setTimeout(() => {
            const allDetails2 = document.querySelectorAll("#user-guide-screen details");
            allDetails2.forEach((details) => {
              details.open = false;
            });
            const guideSection = document.getElementById(guideSectionId);
            if (guideSection) {
              guideSection.open = true;
              guideSection.scrollIntoView({ behavior: "smooth" });
            }
          }, 100);
        });
      }
    };
    const setupDynamicLink = (linkId, guideSectionId) => {
      const observer = new MutationObserver((mutationsList, observer2) => {
        const link = document.getElementById(linkId);
        if (link) {
          setupGuideLink(linkId, guideSectionId);
          observer2.disconnect();
        }
      });
      observer.observe(document.body, { childList: true, subtree: true });
    };
    setupGuideLink("how-to-trip-planner", "guide-trip-planner");
    setupGuideLink("how-to-activities", "guide-activities");
    setupDynamicLink("how-to-journeys", "guide-journeys");
    const allDetails = document.querySelectorAll("#user-guide-screen details");
    allDetails.forEach((details) => {
      details.addEventListener("toggle", (e) => {
        if (details.open) {
          allDetails.forEach((d) => {
            if (d !== details) {
              d.open = false;
            }
          });
        }
      });
    });
  });
})();
