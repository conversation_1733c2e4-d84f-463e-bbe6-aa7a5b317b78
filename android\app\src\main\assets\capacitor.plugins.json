[{"pkg": "@capacitor-community/admob", "classpath": "com.getcapacitor.community.admob.AdMob"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/clipboard", "classpath": "com.capacitorjs.plugins.clipboard.ClipboardPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/geolocation", "classpath": "com.capacitorjs.plugins.geolocation.GeolocationPlugin"}, {"pkg": "@capacitor/push-notifications", "classpath": "com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin"}, {"pkg": "@capacitor/share", "classpath": "com.capacitorjs.plugins.share.SharePlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capawesome/capacitor-file-picker", "classpath": "io.capawesome.capacitorjs.plugins.filepicker.FilePickerPlugin"}, {"pkg": "capacitor-voice-recorder", "classpath": "com.tchvu3.capacitorvoicerecorder.VoiceRecorder"}]