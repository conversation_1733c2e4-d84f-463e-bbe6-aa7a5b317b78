(() => {
  // adsense-loader.js
  var ADS_CLIENT = "ca-pub-4916662846779623";
  var ADS_SLOT = "3226349353";
  function ensureAdScript() {
    if (document.querySelector('script[src*="pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"]')) return;
    const s = document.createElement("script");
    s.async = true;
    s.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${ADS_CLIENT}`;
    s.crossOrigin = "anonymous";
    document.head.appendChild(s);
  }
  function renderAd() {
    const container = document.getElementById("adsense-container");
    if (!container) return;
    if (!container.querySelector("ins.adsbygoogle")) {
      const ins = document.createElement("ins");
      ins.className = "adsbygoogle";
      ins.style.display = "block";
      ins.style.textAlign = "center";
      ins.setAttribute("data-ad-client", ADS_CLIENT);
      ins.setAttribute("data-ad-slot", ADS_SLOT);
      ins.setAttribute("data-ad-layout", "in-article");
      ins.setAttribute("data-ad-format", "fluid");
      container.appendChild(ins);
    }
    if (window.adsbygoogle && Array.isArray(window.adsbygoogle)) {
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (_) {
      }
    }
  }
  function loadAdSenseIfVisible() {
    ensureAdScript();
    const infoScreen = document.getElementById("info-screen");
    const container = document.getElementById("adsense-container");
    if (!infoScreen || !container) return;
    const isHidden = infoScreen.classList.contains("hidden");
    if (!isHidden) {
      renderAd();
      return;
    }
    const observer = new MutationObserver(() => {
      const nowHidden = infoScreen.classList.contains("hidden");
      if (!nowHidden) {
        renderAd();
        observer.disconnect();
      }
    });
    observer.observe(infoScreen, { attributes: true, attributeFilter: ["class"] });
  }
  document.addEventListener("DOMContentLoaded", loadAdSenseIfVisible);
})();
